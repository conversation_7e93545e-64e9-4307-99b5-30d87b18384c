#!/usr/bin/env python3
"""
修复后的CISM训练测试脚本
==========================

测试修复后的优化器状态管理和内存优化
"""

import sys
import os
sys.path.append('.')

def test_fixed_cism_training():
    """测试修复后的CISM训练功能"""
    
    print('=== 修复后的CISM项目端到端训练测试 ===')
    
    # 使用实际存在的文件路径
    MASK_PATH = 'stage2_input/stage2_input_garden/mask.png'
    PLY_PATH = 'outputs/concept/garden_merged_with_concepts_y_fixed.ply'
    OUTPUT_PATH = 'temp_test/test_training_output_fixed.ply'
    REPAIR_PROMPT = 'a vibrant, lush green lawn with dense blades of grass'
    MODEL_PATH = './models/stable-diffusion-2-1-base'
    
    print(f'使用的文件路径:')
    print(f'  掩码文件: {MASK_PATH}')
    print(f'  点云文件: {PLY_PATH}')
    print(f'  输出路径: {OUTPUT_PATH}')
    print(f'  模型路径: {MODEL_PATH}')
    
    try:
        # 1. 创建配置（使用更保守的设置）
        from cism_integration.config import CISMConfig
        config = CISMConfig(
            mask_path=MASK_PATH,
            incomplete_gaussians_path=PLY_PATH,
            output_path=OUTPUT_PATH,
            repair_prompt=REPAIR_PROMPT,
            model_key=MODEL_PATH,
            iterations=3,  # 只测试3次迭代
            save_interval=1,
            log_interval=1,
            # 内存优化设置
            batch_size=1,
            warmup_iter=1,  # 减少预热迭代
        )
        print('✅ 配置创建成功')
        
        # 2. 创建主控制器
        from cism_integration.cism_stage3 import CISMStage3
        cism_stage3 = CISMStage3(config)
        print('✅ CISMStage3主控制器创建成功')
        
        # 3. 运行训练测试
        print('🚀 开始运行修复后的训练测试...')
        result = cism_stage3.run_cism_training(iterations=3)
        
        # 4. 验证训练结果
        print('🔍 验证训练结果...')
        
        # 检查结果是否有效
        if result and isinstance(result, dict):
            if result.get('success', False) or result.get('completed', False):
                print('✅ 训练成功完成')
                return True
            elif result.get('iterations_completed', 0) > 0:
                print(f'⚠️ 训练部分完成：完成了 {result.get("iterations_completed", 0)} 次迭代')
                return True
            else:
                print('❌ 训练未能正常完成')
                print(f'训练结果: {result}')
                return False
        else:
            print('❌ 训练返回无效结果')
            print(f'训练结果: {result}')
            return False
            
    except Exception as e:
        print(f'❌ 端到端训练测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_optimizer_state_fix():
    """专门测试优化器状态管理修复"""

    print('\n=== 优化器状态管理修复测试 ===')

    try:
        import torch
        import torch.nn as nn
        import numpy as np
        from gaussian_splatting.scene.gaussian_model import GaussianModel
        from gaussian_splatting.utils.graphics_utils import BasicPointCloud

        # 创建一个简单的高斯模型
        gaussians = GaussianModel(sh_degree=0)

        # 🔧 修复：正确创建BasicPointCloud
        num_points = 100
        points = np.random.randn(num_points, 3).astype(np.float32)
        colors = np.random.rand(num_points, 3).astype(np.float32)  # 0-1范围的颜色
        normals = np.zeros((num_points, 3), dtype=np.float32)

        # 创建BasicPointCloud对象
        pcd = BasicPointCloud(points=points, colors=colors, normals=normals)

        # 使用正确的参数调用create_from_pcd
        gaussians.create_from_pcd(pcd, spatial_lr_scale=1.0)
        
        # 设置训练
        from argparse import Namespace
        training_args = Namespace(
            position_lr_init=0.00016,
            position_lr_final=0.0000016,
            position_lr_delay_mult=0.01,
            feature_lr=0.0025,
            opacity_lr=0.05,
            scaling_lr=0.001,
            rotation_lr=0.001,
            percent_dense=0.01,
            iterations=100
        )
        
        gaussians.training_setup(training_args)
        print('✅ 高斯模型训练设置成功')
        
        # 测试在没有优化器步骤的情况下调用reset_opacity
        print('🔧 测试修复后的reset_opacity方法...')
        
        # 这应该不会再抛出TypeError
        gaussians.reset_opacity()
        print('✅ reset_opacity调用成功，修复生效！')
        
        return True
        
    except Exception as e:
        print(f'❌ 优化器状态管理测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试修复后的CISM功能...")
    
    # 测试1：优化器状态管理修复
    optimizer_test_passed = test_optimizer_state_fix()
    
    # 测试2：完整的端到端训练
    if optimizer_test_passed:
        training_test_passed = test_fixed_cism_training()
        
        if training_test_passed:
            print('\n🎉 所有测试通过！修复成功！')
            print('✅ 优化器状态管理bug已修复')
            print('✅ 训练可以正常进行')
            print('✅ 重构后的CISM项目功能完全正常！')
        else:
            print('\n⚠️ 优化器修复成功，但训练仍有问题')
            print('可能需要进一步的内存优化或配置调整')
    else:
        print('\n❌ 优化器状态管理修复失败')
        print('需要检查修复代码')
