"""
CISM参数管理模块
================

提供简化的参数容器，替代复杂的配置系统。
遵循单一职责原则，只负责参数存储和验证。

Author: CISM Development Team
Date: 2025-01-11
"""

# 标准库导入
import json
import os
from dataclasses import asdict, dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

# 常量定义
DEFAULT_DEVICE = "cuda"
DEFAULT_ITERATIONS = 1500
DEFAULT_LEARNING_RATE = 0.0025
DEFAULT_GUIDANCE_SCALE_BG = 5.0
DEFAULT_GUIDANCE_SCALE_REPAIR = 15.0


@dataclass
class CISMParams:
    """
    CISM参数容器类
    
    简化的参数管理系统，替代原有的复杂配置架构。
    保持所有功能参数，同时提供清晰的接口和验证机制。
    
    分组说明：
    - 必需/基础参数：I/O路径、设备、分辨率等
    - 训练/学习率：迭代数、各项LR与退火控制
    - ISM：扩散/DDIM/权重与数值稳定相关配置
    - 监督/约束：监督与深度等可选增强
    - 高斯管理：densify/prune/reset阈值与调度
    - I/O与检查点：保存路径/间隔/恢复
    - 高级/性能：xformers/文本缓存/切片等
    """
    
    # === 必需参数 ===
    mask_path: str
    incomplete_gaussians_path: str
    output_path: str
    repair_prompt: str
    
    # === 基础参数 ===
    source_path: Optional[str] = None
    background_prompt: str = "preserve original scene appearance"
    device: str = DEFAULT_DEVICE
    num_workers: int = 4
    resolution: int = -1
    noise_seed: int = 42
    
    # === 训练参数 ===
    mode: str = "ism"  # 训练模式: ism, supervision, hybrid
    iterations: int = DEFAULT_ITERATIONS
    learning_rate: float = DEFAULT_LEARNING_RATE
    batch_size: int = 1
    warmup_iter: int = 1500
    
    # === 学习率配置 ===
    position_lr_init: float = 0.00016
    position_lr_final: float = 0.0000016
    position_lr_delay_mult: float = 0.01
    feature_lr: float = 0.005
    opacity_lr: float = 0.05
    scaling_lr: float = 0.005
    rotation_lr: float = 0.001
    
    # === ISM参数 ===
    model_key: str = "/home/<USER>/Infusion-main-cism/models/stable-diffusion-2-1-base"
    guidance_scale_bg: float = DEFAULT_GUIDANCE_SCALE_BG
    guidance_scale_repair: float = DEFAULT_GUIDANCE_SCALE_REPAIR
    fp16: bool = True
    ism_t_range: Tuple[float, float] = (0.05, 0.95)
    ism_delta_t: int = 1
    delta_t: int = 50
    xs_delta_t: int = 200
    xs_inv_steps: int = 5
    denoise_guidance_scale: float = 1.0
    xs_eta: float = 0.0
    grad_scale: float = 0.1
    
    # === ISM退火策略 ===
    delta_t_start: int = 50
    delta_t_end: int = 1
    enable_delta_t_annealing: bool = True
    
    # === 损失权重 ===
    lambda_bg: float = 0.2
    lambda_repair: float = 0.6
    lambda_boundary: float = 0.15
    lambda_geometry: float = 0.05
    tv_loss_weight: float = 0.0001
    
    # === 监督学习参数 ===
    enable_supervision: bool = False
    supervision_image_path: Optional[str] = None
    supervision_iterations: int = 150
    supervision_learning_rate: float = 0.0025
    lambda_l1: float = 0.8
    lambda_ssim: float = 0.2
    lambda_concept_consistency: float = 0.1
    supervision_mask_dilation: int = 10

    # === 简化的约束参数（遵循原始项目风格）===
    # 核心约束参数 - 遵循Gaussian-Eraser的简化设计
    depth_weight: float = 0.0                    # 深度约束权重（简化命名）
    depth_path: Optional[str] = None             # 深度文件路径（简化命名）
    supervision_weight: float = 0.0              # 监督约束权重（简化命名）
    supervision_path: Optional[str] = None       # 监督图像路径（简化命名）

    # === 向后兼容性参数（保持现有接口）===
    # 注：以下字段建议逐步迁移至简化命名（将通过文档与日志提示），暂不移除以保证兼容
    depth_constraint_weight: float = 0.0
    depth_constraint_enabled: bool = False
    stage2_depth_path: Optional[str] = None
    depth_consistency_threshold: float = 0.1
    depth_background_only: bool = True

    supervision_constraint_weight: float = 0.0
    supervision_constraint_enabled: bool = False
    supervision_background_weight: float = 0.7
    supervision_l1_weight: float = 0.7
    supervision_ssim_weight: float = 0.3

    enable_hybrid_constraints: bool = False
    constraint_balance_strategy: Optional[str] = None  # 弃用参数，默认为None
    
    # === 混合训练参数（已弃用，保留向后兼容）===
    enable_hybrid_training: bool = False
    target_image_path: Optional[str] = None
    # supervision_weight 已移至简化约束参数区，避免重复定义
    ism_weight: float = 0.5
    
    # === IO参数 ===
    save_interval: int = 100
    log_interval: int = 10
    save_intermediate: bool = True
    checkpoint_interval: int = 500
    resume_from_checkpoint: Optional[str] = None
    checkpoint_dir: str = "checkpoints"
    max_checkpoints: int = 5
    
    # === 高斯点云管理参数 ===
    densification_interval: int = 100
    densify_from_iter: int = 500
    densify_until_iter: int = 15000
    densify_grad_threshold: float = 0.0002
    opacity_reset_interval: int = 3000
    percent_dense: float = 0.01
    min_opacity_threshold: float = 0.005
    size_threshold_ratio: float = 0.3
    sh_degree: int = 0
    
    # === 高级选项 ===
    use_depth_constraint: bool = True
    boundary_dilation_size: int = 5
    
    # === 性能优化参数 ===
    vram_O: bool = True
    enable_xformers: bool = True
    enable_text_cache: bool = True
    
    def validate(self) -> None:
        """
        验证参数有效性
        
        Raises:
            FileNotFoundError: 当必需文件不存在时
            ValueError: 当参数值无效时
        """
        # 验证必需文件存在
        self._validate_file_exists(self.mask_path, "掩码文件")
        self._validate_file_exists(self.incomplete_gaussians_path, "高斯点云文件")
        
        # 验证可选文件
        if self.source_path and not Path(self.source_path).exists():
            raise FileNotFoundError(f"场景数据路径不存在: {self.source_path}")
        
        if self.enable_supervision and self.supervision_image_path:
            self._validate_file_exists(self.supervision_image_path, "监督学习参考图像")
        
        # 创建输出目录
        output_dir = Path(self.output_path).parent
        if output_dir:
            output_dir.mkdir(parents=True, exist_ok=True)
        
        # 验证参数范围
        self._validate_parameter_ranges()
        
        # 验证损失权重
        self._validate_loss_weights()
    
    def _validate_file_exists(self, file_path: str, description: str) -> None:
        """验证文件存在性"""
        if not Path(file_path).exists():
            raise FileNotFoundError(f"{description}不存在: {file_path}")
    
    def _validate_parameter_ranges(self) -> None:
        """验证参数范围"""
        if self.iterations <= 0:
            raise ValueError(f"迭代次数必须大于0: {self.iterations}")
        
        if self.learning_rate <= 0:
            raise ValueError(f"学习率必须大于0: {self.learning_rate}")
        
        if self.resolution != -1 and (self.resolution < 64 or self.resolution > 4096):
            raise ValueError(f"分辨率超出有效范围 [64, 4096]: {self.resolution}")
        
        if not (1 <= self.boundary_dilation_size <= 50):
            raise ValueError(f"边界膨胀大小超出有效范围 [1, 50]: {self.boundary_dilation_size}")
    
    def _validate_loss_weights(self) -> None:
        """验证损失权重"""
        total_weight = self.lambda_bg + self.lambda_repair + self.lambda_boundary + self.lambda_geometry
        if abs(total_weight - 1.0) > 0.01:
            print(f"[警告] 损失权重总和不为1.0: {total_weight:.3f}")
        
        if self.enable_supervision:
            supervision_total = self.lambda_l1 + self.lambda_ssim
            if abs(supervision_total - 1.0) > 0.01:
                print(f"[警告] 监督学习损失权重总和不为1.0: {supervision_total:.3f}")
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典格式
        
        Returns:
            包含所有参数的字典
        """
        return asdict(self)
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'CISMParams':
        """
        从字典创建参数对象
        
        Args:
            config_dict: 参数字典
            
        Returns:
            CISMParams实例
        """
        return cls(**config_dict)
    
    def save_json(self, file_path: str) -> None:
        """
        保存参数到JSON文件
        
        Args:
            file_path: JSON文件路径
        """
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False, default=str)
        print(f"[配置] 参数已保存到: {file_path}")
    
    @classmethod
    def load_json(cls, file_path: str) -> 'CISMParams':
        """
        从JSON文件加载参数（向后兼容）
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            CISMParams实例
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        # 处理可能的兼容性问题
        if isinstance(config_dict.get('ism_t_range'), list):
            config_dict['ism_t_range'] = tuple(config_dict['ism_t_range'])
        
        return cls.from_dict(config_dict)
    
    def get_text_prompts(self) -> Dict[str, str]:
        """
        获取文本提示字典（兼容旧API）
        
        Returns:
            包含背景和修复提示的字典
        """
        return {
            "background": self.background_prompt,
            "repair": self.repair_prompt
        }
    
    def get_guidance_scales(self) -> Dict[str, float]:
        """
        获取引导强度字典（兼容旧API）
        
        Returns:
            包含背景和修复引导强度的字典
        """
        return {
            "background": self.guidance_scale_bg,
            "repair": self.guidance_scale_repair
        }
    
    def get_loss_weights(self) -> Dict[str, float]:
        """
        获取损失权重字典（兼容旧API）

        Returns:
            包含各项损失权重的字典
        """
        return {
            "background": self.lambda_bg,
            "repair": self.lambda_repair,
            "boundary": self.lambda_boundary,
            "geometry": self.lambda_geometry,
            "depth_constraint": self.depth_constraint_weight,
            "supervision_constraint": self.supervision_constraint_weight
        }

    def __post_init__(self):
        """
        参数后处理和向后兼容性映射
        遵循原始项目的简单处理风格，同时保持向后兼容性
        """
        # 向后兼容性映射：将旧参数映射到新的简化参数
        if self.depth_constraint_weight > 0 and self.depth_weight == 0.0:
            self.depth_weight = self.depth_constraint_weight
        if self.stage2_depth_path and not self.depth_path:
            self.depth_path = self.stage2_depth_path
        if self.supervision_constraint_weight > 0 and self.supervision_weight == 0.0:
            self.supervision_weight = self.supervision_constraint_weight
        if self.supervision_path is None and self.supervision_image_path:
            self.supervision_path = self.supervision_image_path

        # 反向映射：保持旧接口可用
        if self.depth_weight > 0:
            self.depth_constraint_weight = self.depth_weight
            self.depth_constraint_enabled = True
        if self.depth_path:
            self.stage2_depth_path = self.depth_path
        if self.supervision_weight > 0:
            self.supervision_constraint_weight = self.supervision_weight
            self.supervision_constraint_enabled = True
        if self.supervision_path:
            self.supervision_image_path = self.supervision_path

        # 轻量弃用提示（一次性打印，不影响执行）
        try:
            deprecated_hits = []
            # 只有用户主动设置了弃用参数才警告
            if self.constraint_balance_strategy is not None:
                deprecated_hits.append('constraint_balance_strategy (推荐使用固定权重的线性组合)')
            if self.enable_hybrid_training:
                deprecated_hits.append('enable_hybrid_training (已不推荐，建议使用简化约束开关)')
            if deprecated_hits:
                print("[提示] 发现不推荐使用的旧参数: " + "; ".join(deprecated_hits))
        except Exception:
            pass
    
    # === 兼容性方法（支持属性式访问）===
    def get(self, key: str, default: Any = None) -> Any:
        """
        字典式访问接口（向后兼容）
        
        Args:
            key: 参数键
            default: 默认值
            
        Returns:
            参数值或默认值
        """
        return getattr(self, key, default)
    
    def keys(self) -> List[str]:
        """
        获取所有参数键（向后兼容）
        
        Returns:
            参数键列表
        """
        return list(self.__dataclass_fields__.keys())