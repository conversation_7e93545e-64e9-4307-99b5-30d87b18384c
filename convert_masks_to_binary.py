#!/usr/bin/env python3
"""
RedNet掩码转换脚本：将语义分割掩码转换为InFusion期望的二值掩码

转换策略：
1. 策略A：将所有非背景区域(值!=0)转换为修复区域(255)
2. 策略B：选择特定类别作为修复区域
3. 策略C：基于像素密度选择主要物体类别

使用方法：
python convert_masks_to_binary.py --strategy A --input_dir data/rednet/seg --output_dir data/rednet/seg_binary
"""

import cv2
import numpy as np
import os
import argparse
from pathlib import Path
import json

def analyze_mask_statistics(seg_dir):
    """分析掩码统计信息，帮助选择转换策略"""
    print("📊 分析掩码统计信息...")
    
    mask_files = [f for f in os.listdir(seg_dir) if f.endswith('.png')]
    
    # 统计所有掩码的类别分布
    global_class_counts = {}
    total_pixels = 0
    
    for filename in mask_files[:10]:  # 分析前10个文件
        mask_path = os.path.join(seg_dir, filename)
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        
        unique_values, counts = np.unique(mask, return_counts=True)
        total_pixels += mask.size
        
        for val, count in zip(unique_values, counts):
            if val not in global_class_counts:
                global_class_counts[val] = 0
            global_class_counts[val] += count
    
    # 计算类别占比
    class_percentages = {}
    for class_id, count in global_class_counts.items():
        class_percentages[class_id] = (count / total_pixels) * 100
    
    # 排序并显示主要类别
    sorted_classes = sorted(class_percentages.items(), key=lambda x: x[1], reverse=True)
    
    print("主要类别分布（前15个）：")
    for class_id, percentage in sorted_classes[:15]:
        print(f"  类别 {class_id}: {percentage:.2f}%")
    
    return global_class_counts, class_percentages

def strategy_A_all_objects(mask):
    """策略A：将所有非背景区域转换为修复区域"""
    binary_mask = np.zeros_like(mask)
    binary_mask[mask != 0] = 255  # 所有非0值都变成255
    return binary_mask

def strategy_B_specific_classes(mask, target_classes):
    """策略B：将特定类别转换为修复区域"""
    binary_mask = np.zeros_like(mask)
    for class_id in target_classes:
        binary_mask[mask == class_id] = 255
    return binary_mask

def strategy_C_major_objects(mask, class_percentages, threshold=0.1):
    """策略C：将占比超过阈值的主要物体类别转换为修复区域"""
    binary_mask = np.zeros_like(mask)
    
    # 找出占比超过阈值的类别（排除背景类别0）
    major_classes = []
    for class_id, percentage in class_percentages.items():
        if class_id != 0 and percentage > threshold:
            major_classes.append(class_id)
    
    for class_id in major_classes:
        binary_mask[mask == class_id] = 255
    
    return binary_mask, major_classes

def strategy_D_furniture_objects(mask):
    """策略D：基于RedNet数据集的家具类别（推测）"""
    # 基于RedNet数据集特征，推测常见家具类别
    # 这些值需要根据实际数据集调整
    furniture_classes = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
    
    binary_mask = np.zeros_like(mask)
    for class_id in furniture_classes:
        binary_mask[mask == class_id] = 255
    
    return binary_mask

def convert_masks(input_dir, output_dir, strategy='A', target_classes=None, threshold=0.1):
    """转换掩码文件"""
    
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 分析掩码统计（用于策略C）
    if strategy == 'C':
        global_class_counts, class_percentages = analyze_mask_statistics(input_dir)
    else:
        class_percentages = None
    
    # 获取所有掩码文件
    mask_files = [f for f in os.listdir(input_dir) if f.endswith('.png')]
    mask_files.sort()
    
    print(f"\n🔄 开始转换 {len(mask_files)} 个掩码文件...")
    print(f"转换策略: {strategy}")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    
    conversion_stats = {
        'total_files': len(mask_files),
        'successful': 0,
        'failed': 0,
        'strategy': strategy
    }
    
    for i, filename in enumerate(mask_files):
        try:
            # 读取原始掩码
            input_path = os.path.join(input_dir, filename)
            output_path = os.path.join(output_dir, filename)
            
            mask = cv2.imread(input_path, cv2.IMREAD_GRAYSCALE)
            if mask is None:
                print(f"❌ 无法读取: {filename}")
                conversion_stats['failed'] += 1
                continue
            
            # 根据策略转换
            if strategy == 'A':
                binary_mask = strategy_A_all_objects(mask)
                
            elif strategy == 'B':
                if target_classes is None:
                    target_classes = [1, 2, 3, 4, 5]  # 默认类别
                binary_mask = strategy_B_specific_classes(mask, target_classes)
                
            elif strategy == 'C':
                binary_mask, major_classes = strategy_C_major_objects(mask, class_percentages, threshold)
                if i == 0:  # 只在第一个文件时显示
                    print(f"主要类别: {major_classes}")
                    
            elif strategy == 'D':
                binary_mask = strategy_D_furniture_objects(mask)
                
            else:
                raise ValueError(f"未知策略: {strategy}")
            
            # 保存转换后的掩码
            cv2.imwrite(output_path, binary_mask)
            conversion_stats['successful'] += 1
            
            # 显示进度
            if (i + 1) % 10 == 0 or i == 0:
                mask_ratio = np.sum(binary_mask == 255) / binary_mask.size * 100
                print(f"  {i+1:3d}/{len(mask_files)} - {filename}: 修复区域 {mask_ratio:.2f}%")
                
        except Exception as e:
            print(f"❌ 转换失败 {filename}: {e}")
            conversion_stats['failed'] += 1
    
    # 保存转换统计
    stats_path = os.path.join(output_dir, 'conversion_stats.json')
    with open(stats_path, 'w') as f:
        json.dump(conversion_stats, f, indent=2)
    
    print(f"\n✅ 转换完成!")
    print(f"成功: {conversion_stats['successful']} 个文件")
    print(f"失败: {conversion_stats['failed']} 个文件")
    print(f"统计信息保存到: {stats_path}")
    
    return conversion_stats

def preview_conversion(input_dir, strategy='A', num_samples=3):
    """预览转换效果"""
    print(f"\n🔍 预览转换效果 (策略 {strategy})...")
    
    mask_files = [f for f in os.listdir(input_dir) if f.endswith('.png')][:num_samples]
    
    if strategy == 'C':
        global_class_counts, class_percentages = analyze_mask_statistics(input_dir)
    else:
        class_percentages = None
    
    for filename in mask_files:
        mask_path = os.path.join(input_dir, filename)
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        
        if strategy == 'A':
            binary_mask = strategy_A_all_objects(mask)
        elif strategy == 'C':
            binary_mask, major_classes = strategy_C_major_objects(mask, class_percentages, 0.1)
        elif strategy == 'D':
            binary_mask = strategy_D_furniture_objects(mask)
        else:
            binary_mask = strategy_A_all_objects(mask)
        
        original_classes = len(np.unique(mask))
        mask_ratio = np.sum(binary_mask == 255) / binary_mask.size * 100
        
        print(f"  {filename}:")
        print(f"    原始类别数: {original_classes}")
        print(f"    修复区域占比: {mask_ratio:.2f}%")

def main():
    parser = argparse.ArgumentParser(description="转换RedNet掩码为InFusion二值掩码")
    parser.add_argument('--strategy', choices=['A', 'B', 'C', 'D'], default='A',
                       help='转换策略: A=所有物体, B=特定类别, C=主要物体, D=家具类别')
    parser.add_argument('--input_dir', default='data/rednet/seg',
                       help='输入掩码目录')
    parser.add_argument('--output_dir', default='data/rednet/seg_binary',
                       help='输出掩码目录')
    parser.add_argument('--target_classes', nargs='+', type=int,
                       help='策略B的目标类别列表')
    parser.add_argument('--threshold', type=float, default=0.1,
                       help='策略C的类别占比阈值')
    parser.add_argument('--preview', action='store_true',
                       help='只预览转换效果，不实际转换')
    
    args = parser.parse_args()
    
    print("🎭 RedNet掩码转换工具")
    print("=" * 50)
    
    # 检查输入目录
    if not os.path.exists(args.input_dir):
        print(f"❌ 输入目录不存在: {args.input_dir}")
        return
    
    # 策略说明
    strategies_info = {
        'A': "将所有非背景区域(值!=0)转换为修复区域(255)",
        'B': "将特定类别转换为修复区域",
        'C': "将占比超过阈值的主要物体类别转换为修复区域",
        'D': "将推测的家具类别转换为修复区域"
    }
    
    print(f"选择的策略: {args.strategy} - {strategies_info[args.strategy]}")
    
    if args.preview:
        # 预览模式
        preview_conversion(args.input_dir, args.strategy)
    else:
        # 实际转换
        stats = convert_masks(
            args.input_dir, 
            args.output_dir, 
            args.strategy,
            args.target_classes,
            args.threshold
        )
        
        print(f"\n📋 转换完成统计:")
        print(f"策略: {stats['strategy']}")
        print(f"总文件数: {stats['total_files']}")
        print(f"成功转换: {stats['successful']}")
        print(f"转换失败: {stats['failed']}")
        
        if stats['successful'] > 0:
            print(f"\n🎯 下一步:")
            print(f"1. 检查转换结果: {args.output_dir}")
            print(f"2. 使用新掩码重新训练Stage 1:")
            print(f"   python train.py -s data/rednet_binary -m outputs/output/stage1_rednet_binary --mask_training")

if __name__ == "__main__":
    main()
