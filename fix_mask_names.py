#!/usr/bin/env python3
"""
修复掩码文件名匹配问题
将掩码文件重命名以匹配对应的图像文件
"""

import os
import shutil
from pathlib import Path

def fix_mask_names():
    """修复掩码文件名以匹配图像文件"""
    
    # 定义路径
    data_dir = Path("/home/<USER>/Infusion-main-cism/data/book")
    images_dir = data_dir / "images"
    seg_dir = data_dir / "seg"
    backup_dir = data_dir / "seg_backup"
    
    print("🔍 开始修复掩码文件名...")
    
    # 1. 创建备份目录
    if not backup_dir.exists():
        backup_dir.mkdir()
        print(f"✅ 创建备份目录: {backup_dir}")
    
    # 2. 获取所有文件列表并排序
    image_files = sorted([f for f in images_dir.glob("*.jpg")])
    mask_files = sorted([f for f in seg_dir.glob("*.png")])
    
    print(f"📊 找到 {len(image_files)} 个图像文件")
    print(f"📊 找到 {len(mask_files)} 个掩码文件")
    
    # 3. 检查数量是否匹配
    if len(image_files) != len(mask_files):
        print(f"❌ 错误：图像文件数量({len(image_files)})与掩码文件数量({len(mask_files)})不匹配！")
        return False
    
    # 4. 备份原始掩码文件
    print("💾 备份原始掩码文件...")
    for mask_file in mask_files:
        backup_path = backup_dir / mask_file.name
        if not backup_path.exists():
            shutil.copy2(mask_file, backup_path)
    print("✅ 备份完成")
    
    # 5. 创建临时目录用于重命名
    temp_dir = data_dir / "seg_temp"
    if temp_dir.exists():
        shutil.rmtree(temp_dir)
    temp_dir.mkdir()
    
    # 6. 按顺序重命名掩码文件
    print("🔄 开始重命名掩码文件...")
    rename_pairs = []
    
    for i, (image_file, mask_file) in enumerate(zip(image_files, mask_files)):
        # 获取图像文件的基础名称（不含扩展名）
        image_base = image_file.stem  # 例如：20220811_093730
        
        # 新的掩码文件名（保持.png扩展名）
        new_mask_name = f"{image_base}.png"
        new_mask_path = temp_dir / new_mask_name
        
        # 复制到临时目录
        shutil.copy2(mask_file, new_mask_path)
        
        rename_pairs.append((mask_file.name, new_mask_name))
        
        if i < 5:  # 显示前5个示例
            print(f"  {mask_file.name} -> {new_mask_name}")
    
    if len(rename_pairs) > 5:
        print(f"  ... 还有 {len(rename_pairs) - 5} 个文件")
    
    # 7. 删除原始seg目录内容并移动新文件
    print("📁 更新seg目录...")
    for old_file in seg_dir.glob("*.png"):
        old_file.unlink()
    
    # 移动重命名后的文件到seg目录
    for new_file in temp_dir.glob("*.png"):
        final_path = seg_dir / new_file.name
        shutil.move(new_file, final_path)
    
    # 清理临时目录
    temp_dir.rmdir()
    
    # 8. 验证结果
    print("🔍 验证重命名结果...")
    new_mask_files = sorted([f for f in seg_dir.glob("*.png")])
    
    success_count = 0
    for image_file in image_files:
        expected_mask = seg_dir / f"{image_file.stem}.png"
        if expected_mask.exists():
            success_count += 1
        else:
            print(f"❌ 缺少掩码文件: {expected_mask.name}")
    
    print(f"✅ 成功匹配 {success_count}/{len(image_files)} 个文件")
    
    # 9. 显示匹配示例
    print("\n📋 文件匹配示例:")
    for i in range(min(5, len(image_files))):
        image_name = image_files[i].name
        mask_name = f"{image_files[i].stem}.png"
        print(f"  {image_name} <-> {mask_name}")
    
    if success_count == len(image_files):
        print("\n🎉 所有掩码文件已成功重命名并匹配！")
        print(f"💾 原始文件已备份到: {backup_dir}")
        return True
    else:
        print(f"\n❌ 重命名过程中出现问题，成功率: {success_count}/{len(image_files)}")
        return False

def verify_matching():
    """验证图像和掩码文件的匹配情况"""
    
    data_dir = Path("/home/<USER>/Infusion-main-cism/data/book")
    images_dir = data_dir / "images"
    seg_dir = data_dir / "seg"
    
    print("🔍 验证文件匹配情况...")
    
    image_files = sorted([f for f in images_dir.glob("*.jpg")])
    
    matched = 0
    unmatched = []
    
    for image_file in image_files:
        expected_mask = seg_dir / f"{image_file.stem}.png"
        if expected_mask.exists():
            matched += 1
        else:
            unmatched.append(image_file.name)
    
    print(f"✅ 匹配成功: {matched}/{len(image_files)} 个文件")
    
    if unmatched:
        print("❌ 未匹配的图像文件:")
        for name in unmatched[:10]:  # 只显示前10个
            print(f"  {name}")
        if len(unmatched) > 10:
            print(f"  ... 还有 {len(unmatched) - 10} 个")
    
    return matched == len(image_files)

if __name__ == "__main__":
    print("🚀 开始修复掩码文件名匹配问题")
    print("=" * 50)
    
    # 首先验证当前状态
    if verify_matching():
        print("✅ 文件已经匹配，无需修复")
    else:
        # 执行修复
        if fix_mask_names():
            print("\n🎉 修复完成！")
            # 再次验证
            verify_matching()
        else:
            print("\n❌ 修复失败，请检查错误信息")
    
    print("=" * 50)
    print("🏁 脚本执行完成")
