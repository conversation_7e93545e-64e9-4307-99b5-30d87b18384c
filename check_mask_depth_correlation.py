#!/usr/bin/env python3
"""
检查掩码区域在深度图中的表现
分析Stage 1训练是否正确处理了掩码区域
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
import os
from PIL import Image
import argparse

def load_mask(mask_path):
    """加载掩码图像"""
    try:
        mask = cv2.imread(mask_path, cv2.IMREAD_GRAYSCALE)
        if mask is None:
            # 尝试用PIL加载
            mask = np.array(Image.open(mask_path).convert('L'))
        return mask
    except Exception as e:
        print(f"加载掩码失败: {e}")
        return None

def analyze_mask_depth_correlation(mask_path, depth_path, render_path=None, output_path=None):
    """分析掩码区域与深度图的对应关系"""
    print(f"\n🔍 分析掩码-深度对应关系")
    print(f"掩码文件: {os.path.basename(mask_path)}")
    print(f"深度文件: {os.path.basename(depth_path)}")
    
    # 加载掩码
    mask = load_mask(mask_path)
    if mask is None:
        print("❌ 无法加载掩码文件")
        return None
    
    # 加载深度图
    try:
        depth = np.load(depth_path)
    except Exception as e:
        print(f"❌ 无法加载深度文件: {e}")
        return None
    
    # 加载渲染图像（如果提供）
    render_img = None
    if render_path and os.path.exists(render_path):
        try:
            render_img = cv2.imread(render_path)
            render_img = cv2.cvtColor(render_img, cv2.COLOR_BGR2RGB)
        except:
            render_img = None
    
    print(f"掩码尺寸: {mask.shape}")
    print(f"深度图尺寸: {depth.shape}")
    
    # 调整掩码尺寸以匹配深度图
    if mask.shape != depth.shape:
        print(f"调整掩码尺寸从 {mask.shape} 到 {depth.shape}")
        mask = cv2.resize(mask, (depth.shape[1], depth.shape[0]))
    
    # 二值化掩码 (白色=255为需要修复的区域)
    mask_binary = (mask > 127).astype(np.uint8)
    mask_area = mask_binary.sum()
    total_area = mask.size
    mask_ratio = mask_area / total_area * 100
    
    print(f"掩码区域占比: {mask_ratio:.2f}%")
    
    # 分析掩码区域的深度值
    mask_region_depth = depth[mask_binary == 1]
    background_depth = depth[mask_binary == 0]
    
    if len(mask_region_depth) > 0:
        print(f"\n📊 掩码区域深度分析:")
        print(f"掩码区域像素数: {len(mask_region_depth)}")
        print(f"掩码区域深度范围: {mask_region_depth.min():.6f} - {mask_region_depth.max():.6f}")
        print(f"掩码区域深度均值: {mask_region_depth.mean():.6f}")
        print(f"掩码区域深度标准差: {mask_region_depth.std():.6f}")
        
        # 检查掩码区域是否有特殊值
        zero_count = (mask_region_depth == 0).sum()
        inf_count = np.isinf(mask_region_depth).sum()
        nan_count = np.isnan(mask_region_depth).sum()
        
        print(f"\n🔍 掩码区域特殊值检查:")
        print(f"零值数量: {zero_count} ({zero_count/len(mask_region_depth)*100:.2f}%)")
        print(f"无穷大值数量: {inf_count}")
        print(f"NaN值数量: {nan_count}")
        
        # 与背景区域对比
        if len(background_depth) > 0:
            print(f"\n📊 背景区域深度分析:")
            print(f"背景区域像素数: {len(background_depth)}")
            print(f"背景区域深度范围: {background_depth.min():.6f} - {background_depth.max():.6f}")
            print(f"背景区域深度均值: {background_depth.mean():.6f}")
            print(f"背景区域深度标准差: {background_depth.std():.6f}")
            
            # 统计差异
            depth_diff = abs(mask_region_depth.mean() - background_depth.mean())
            print(f"\n📈 掩码与背景深度差异: {depth_diff:.6f}")
    
    # 可视化分析
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    
    # 原始掩码
    axes[0,0].imshow(mask, cmap='gray')
    axes[0,0].set_title('原始掩码')
    axes[0,0].axis('off')
    
    # 二值化掩码
    axes[0,1].imshow(mask_binary, cmap='gray')
    axes[0,1].set_title('二值化掩码')
    axes[0,1].axis('off')
    
    # 渲染图像（如果有）
    if render_img is not None:
        axes[0,2].imshow(render_img)
        axes[0,2].set_title('渲染图像')
    else:
        axes[0,2].text(0.5, 0.5, '无渲染图像', ha='center', va='center', transform=axes[0,2].transAxes)
    axes[0,2].axis('off')
    
    # 深度图
    im1 = axes[1,0].imshow(depth, cmap='viridis')
    axes[1,0].set_title('深度图')
    axes[1,0].axis('off')
    plt.colorbar(im1, ax=axes[1,0])
    
    # 掩码区域深度
    masked_depth = depth.copy()
    masked_depth[mask_binary == 0] = np.nan  # 将非掩码区域设为NaN
    im2 = axes[1,1].imshow(masked_depth, cmap='viridis')
    axes[1,1].set_title('掩码区域深度')
    axes[1,1].axis('off')
    plt.colorbar(im2, ax=axes[1,1])
    
    # 掩码叠加深度图
    depth_normalized = (depth - depth.min()) / (depth.max() - depth.min())
    overlay = np.zeros((*depth.shape, 3))
    overlay[:,:,0] = depth_normalized  # 深度作为红色通道
    overlay[:,:,1] = depth_normalized  # 深度作为绿色通道
    overlay[:,:,2] = depth_normalized  # 深度作为蓝色通道
    
    # 掩码区域用红色高亮
    overlay[mask_binary == 1, 0] = 1.0  # 红色
    overlay[mask_binary == 1, 1] = 0.0  # 绿色
    overlay[mask_binary == 1, 2] = 0.0  # 蓝色
    
    axes[1,2].imshow(overlay)
    axes[1,2].set_title('掩码叠加深度图\n(红色=掩码区域)')
    axes[1,2].axis('off')
    
    plt.tight_layout()
    
    # 保存结果
    if output_path:
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        print(f"可视化结果已保存: {output_path}")
    else:
        output_name = f"mask_depth_analysis_{os.path.basename(depth_path).replace('.npy', '.png')}"
        plt.savefig(output_name, dpi=150, bbox_inches='tight')
        print(f"可视化结果已保存: {output_name}")
    
    plt.close()
    
    # 生成分析报告
    analysis_result = {
        'mask_ratio': mask_ratio,
        'mask_region_depth_stats': {
            'count': len(mask_region_depth),
            'mean': mask_region_depth.mean() if len(mask_region_depth) > 0 else 0,
            'std': mask_region_depth.std() if len(mask_region_depth) > 0 else 0,
            'min': mask_region_depth.min() if len(mask_region_depth) > 0 else 0,
            'max': mask_region_depth.max() if len(mask_region_depth) > 0 else 0,
            'zero_ratio': zero_count/len(mask_region_depth)*100 if len(mask_region_depth) > 0 else 0
        },
        'background_depth_stats': {
            'count': len(background_depth),
            'mean': background_depth.mean() if len(background_depth) > 0 else 0,
            'std': background_depth.std() if len(background_depth) > 0 else 0
        } if len(background_depth) > 0 else None
    }
    
    # 判断掩码训练效果
    print(f"\n🎯 掩码训练效果评估:")
    
    if len(mask_region_depth) == 0:
        print("❌ 掩码区域无深度值，可能掩码文件有问题")
        quality = "失败"
    elif zero_count / len(mask_region_depth) > 0.5:
        print("⚠️ 掩码区域有大量零值，训练可能不完整")
        quality = "需要改进"
    elif mask_region_depth.std() < background_depth.std() * 0.5:
        print("⚠️ 掩码区域深度变化过小，可能训练不充分")
        quality = "一般"
    else:
        print("✅ 掩码区域有合理的深度分布")
        quality = "良好"
    
    analysis_result['quality_assessment'] = quality
    
    return analysis_result

def main():
    parser = argparse.ArgumentParser(description='检查掩码区域在深度图中的表现')
    parser.add_argument('--mask_path', type=str, required=True, help='掩码文件路径')
    parser.add_argument('--depth_path', type=str, required=True, help='深度图文件路径')
    parser.add_argument('--render_path', type=str, help='渲染图像路径（可选）')
    parser.add_argument('--output_path', type=str, help='输出可视化文件路径')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.mask_path):
        print(f"❌ 掩码文件不存在: {args.mask_path}")
        return
    
    if not os.path.exists(args.depth_path):
        print(f"❌ 深度文件不存在: {args.depth_path}")
        return
    
    result = analyze_mask_depth_correlation(
        args.mask_path, 
        args.depth_path, 
        args.render_path, 
        args.output_path
    )
    
    if result:
        print(f"\n📋 分析完成，掩码训练质量: {result['quality_assessment']}")

if __name__ == "__main__":
    main()
