#!/usr/bin/env python3
"""
分析用户提供的深度图图像
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
import argparse
from PIL import Image
import os

def analyze_depth_image_from_visual(image_path):
    """从可视化的深度图图像分析深度质量"""
    print(f"\n🔍 分析深度图可视化: {os.path.basename(image_path)}")
    
    try:
        # 加载图像
        img = cv2.imread(image_path)
        if img is None:
            img = np.array(Image.open(image_path))
        else:
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    except Exception as e:
        print(f"❌ 无法加载图像: {e}")
        return None
    
    print(f"图像尺寸: {img.shape}")
    
    # 转换为灰度图进行分析
    if len(img.shape) == 3:
        gray = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
    else:
        gray = img
    
    # 分析颜色分布（深度图的颜色映射）
    print(f"\n🎨 颜色分布分析:")
    
    # 分析RGB通道
    if len(img.shape) == 3:
        r_channel = img[:,:,0]
        g_channel = img[:,:,1] 
        b_channel = img[:,:,2]
        
        print(f"  红色通道范围: {r_channel.min()} - {r_channel.max()}")
        print(f"  绿色通道范围: {g_channel.min()} - {g_channel.max()}")
        print(f"  蓝色通道范围: {b_channel.min()} - {b_channel.max()}")
        
        # 分析主要颜色区域
        # 蓝色区域（近距离）
        blue_mask = (b_channel > 200) & (r_channel < 100) & (g_channel < 200)
        blue_ratio = blue_mask.sum() / img.size * 100
        
        # 绿色区域（中等距离）
        green_mask = (g_channel > 200) & (r_channel < 200) & (b_channel < 200)
        green_ratio = green_mask.sum() / img.size * 100
        
        # 红色/橙色区域（远距离）
        red_mask = (r_channel > 200) & (g_channel < 150) & (b_channel < 100)
        red_ratio = red_mask.sum() / img.size * 100
        
        # 黄色区域（中远距离）
        yellow_mask = (r_channel > 200) & (g_channel > 200) & (b_channel < 150)
        yellow_ratio = yellow_mask.sum() / img.size * 100
        
        print(f"\n🏞️ 深度层次分析:")
        print(f"  蓝色区域 (近景): {blue_ratio:.1f}%")
        print(f"  绿色区域 (中景): {green_ratio:.1f}%")
        print(f"  黄色区域 (中远景): {yellow_ratio:.1f}%")
        print(f"  红色区域 (远景): {red_ratio:.1f}%")
    
    # 分析深度图的连续性
    grad_x = np.abs(np.gradient(gray.astype(float), axis=1))
    grad_y = np.abs(np.gradient(gray.astype(float), axis=0))
    grad_magnitude = np.sqrt(grad_x**2 + grad_y**2)
    
    avg_gradient = grad_magnitude.mean()
    max_gradient = grad_magnitude.max()
    
    print(f"\n📈 图像连续性分析:")
    print(f"  平均梯度: {avg_gradient:.2f}")
    print(f"  最大梯度: {max_gradient:.2f}")
    print(f"  高梯度像素比例: {(grad_magnitude > avg_gradient * 2).sum() / grad_magnitude.size * 100:.2f}%")
    
    # 检测异常区域
    print(f"\n🔍 异常检测:")
    
    # 检测纯色区域（可能的问题区域）
    if len(img.shape) == 3:
        # 检测纯黑色区域
        black_mask = (r_channel < 10) & (g_channel < 10) & (b_channel < 10)
        black_ratio = black_mask.sum() / img.size * 100
        
        # 检测纯白色区域
        white_mask = (r_channel > 245) & (g_channel > 245) & (b_channel > 245)
        white_ratio = white_mask.sum() / img.size * 100
        
        print(f"  纯黑色区域: {black_ratio:.2f}%")
        print(f"  纯白色区域: {white_ratio:.2f}%")
    
    # 检测边缘锐利度
    edges = cv2.Canny(gray, 50, 150)
    edge_ratio = (edges > 0).sum() / edges.size * 100
    
    print(f"  边缘像素比例: {edge_ratio:.2f}%")
    
    # 分析特定区域（中央的蓝色矩形区域）
    print(f"\n🔷 特殊区域分析:")
    
    if len(img.shape) == 3:
        # 检测中央蓝色矩形区域
        center_blue_mask = (b_channel > 150) & (r_channel < 100) & (g_channel < 150)
        
        # 找到蓝色区域的边界框
        blue_coords = np.where(center_blue_mask)
        if len(blue_coords[0]) > 0:
            min_y, max_y = blue_coords[0].min(), blue_coords[0].max()
            min_x, max_x = blue_coords[1].min(), blue_coords[1].max()
            
            blue_height = max_y - min_y
            blue_width = max_x - min_x
            blue_area = center_blue_mask.sum()
            
            print(f"  中央蓝色区域尺寸: {blue_width} x {blue_height}")
            print(f"  蓝色区域面积: {blue_area} 像素")
            print(f"  蓝色区域占比: {blue_area / img.size * 100:.2f}%")
            
            # 检查蓝色区域是否规整
            expected_area = blue_width * blue_height
            fill_ratio = blue_area / expected_area if expected_area > 0 else 0
            print(f"  区域填充率: {fill_ratio:.2f}")
    
    # 质量评估
    print(f"\n🎯 深度图质量评估:")
    
    quality_score = 0
    issues = []
    
    # 颜色多样性评分 (30分)
    if len(img.shape) == 3:
        color_diversity = len(np.unique(img.reshape(-1, 3), axis=0))
        if color_diversity > 1000:
            quality_score += 30
        elif color_diversity > 500:
            quality_score += 25
            issues.append("颜色多样性略低")
        elif color_diversity > 100:
            quality_score += 15
            issues.append("颜色多样性较低")
        else:
            quality_score += 5
            issues.append("颜色多样性很低")
        
        print(f"  颜色多样性: {color_diversity} 种颜色")
    
    # 连续性评分 (25分)
    if avg_gradient < 20:
        quality_score += 25
    elif avg_gradient < 40:
        quality_score += 20
        issues.append("深度变化略大")
    elif avg_gradient < 80:
        quality_score += 10
        issues.append("深度变化较大")
    else:
        quality_score += 5
        issues.append("深度变化过大")
    
    # 层次分布评分 (25分)
    if len(img.shape) == 3:
        layer_balance = min(blue_ratio, green_ratio, yellow_ratio, red_ratio)
        if layer_balance > 5:
            quality_score += 25
        elif layer_balance > 2:
            quality_score += 20
            issues.append("深度层次分布不够均匀")
        elif layer_balance > 0.5:
            quality_score += 10
            issues.append("深度层次分布不均匀")
        else:
            quality_score += 5
            issues.append("深度层次分布严重不均匀")
    
    # 异常区域评分 (20分)
    if len(img.shape) == 3:
        anomaly_ratio = black_ratio + white_ratio
        if anomaly_ratio < 1:
            quality_score += 20
        elif anomaly_ratio < 5:
            quality_score += 15
            issues.append("存在少量异常区域")
        elif anomaly_ratio < 10:
            quality_score += 10
            issues.append("存在较多异常区域")
        else:
            quality_score += 5
            issues.append("存在大量异常区域")
    
    print(f"\n📊 总体质量评分: {quality_score}/100")
    
    if quality_score >= 85:
        quality_level = "优秀"
        recommendation = "✅ 深度图质量优秀，场景层次丰富，适合进行后续处理"
    elif quality_score >= 70:
        quality_level = "良好"
        recommendation = "✅ 深度图质量良好，可以进行后续处理"
    elif quality_score >= 55:
        quality_level = "一般"
        recommendation = "⚠️ 深度图质量一般，建议检查训练参数"
    else:
        quality_level = "较差"
        recommendation = "❌ 深度图质量较差，建议重新训练"
    
    print(f"质量等级: {quality_level}")
    print(f"建议: {recommendation}")
    
    if issues:
        print(f"\n⚠️ 发现的问题:")
        for issue in issues:
            print(f"  - {issue}")
    
    # 生成分析可视化
    create_analysis_visualization(img, gray, grad_magnitude, image_path)
    
    return {
        'quality_score': quality_score,
        'quality_level': quality_level,
        'issues': issues,
        'recommendation': recommendation
    }

def create_analysis_visualization(img, gray, grad_magnitude, original_path):
    """创建分析可视化图"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 原始深度图
    axes[0,0].imshow(img)
    axes[0,0].set_title('Original Depth Visualization')
    axes[0,0].axis('off')
    
    # 灰度图
    axes[0,1].imshow(gray, cmap='gray')
    axes[0,1].set_title('Grayscale')
    axes[0,1].axis('off')
    
    # 梯度图
    axes[1,0].imshow(grad_magnitude, cmap='hot')
    axes[1,0].set_title('Gradient Magnitude')
    axes[1,0].axis('off')
    
    # 直方图
    if len(img.shape) == 3:
        axes[1,1].hist(img[:,:,0].flatten(), bins=50, alpha=0.7, label='Red', color='red')
        axes[1,1].hist(img[:,:,1].flatten(), bins=50, alpha=0.7, label='Green', color='green')
        axes[1,1].hist(img[:,:,2].flatten(), bins=50, alpha=0.7, label='Blue', color='blue')
        axes[1,1].legend()
    else:
        axes[1,1].hist(gray.flatten(), bins=50, color='gray')
    axes[1,1].set_title('Color Distribution')
    axes[1,1].set_xlabel('Pixel Value')
    axes[1,1].set_ylabel('Frequency')
    
    plt.tight_layout()
    
    output_path = f"depth_image_analysis_{os.path.basename(original_path)}"
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    print(f"\n📊 分析可视化已保存: {output_path}")
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='分析深度图可视化图像')
    parser.add_argument('--image_path', type=str, required=True, help='深度图图像路径')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image_path):
        print(f"❌ 图像文件不存在: {args.image_path}")
        return
    
    result = analyze_depth_image_from_visual(args.image_path)
    
    if result:
        print(f"\n✅ 分析完成")

if __name__ == "__main__":
    main()
