# 📊 Stage 1渲染质量评估指南

## 🎯 渲染质量评估概述

Stage 1的渲染质量直接影响后续Stage 2-3的效果。高质量的Stage 1输出应该具备清晰的几何结构、准确的颜色表现、以及可靠的深度信息。

---

## 📋 第一部分：视觉质量评估标准

### ✅ **1. 整体渲染质量**

#### **优秀质量特征：**
- **清晰度**: 图像边缘锐利，无明显模糊
- **颜色准确性**: 与原始图像颜色一致，无明显色偏
- **几何一致性**: 物体形状和比例正确
- **光照合理性**: 阴影和高光符合场景光照
- **无伪影**: 没有明显的渲染伪影或噪点

#### **质量检查要点：**
```
✅ 物体边缘清晰，无锯齿
✅ 纹理细节保留完整
✅ 颜色饱和度和亮度自然
✅ 阴影过渡平滑
✅ 无浮点伪影或"飞点"
```

### ✅ **2. 掩码区域质量**

#### **关键评估指标：**
- **掩码边界**: 掩码区域边界应该清晰准确
- **背景保持**: 非掩码区域应该完美重建
- **过渡自然**: 掩码边界处无明显断层
- **几何连续性**: 掩码区域的几何结构合理

#### **RedNet数据集特定要求：**
```
掩码区域占比: ~12% (适中，有利于训练)
边界复杂度: 中等（不会过于复杂导致训练困难）
背景完整性: 87.88%的背景区域应该完美重建
```

### ✅ **3. 多视角一致性**

#### **一致性检查：**
- **几何一致性**: 同一物体在不同视角下形状一致
- **颜色一致性**: 相同表面在不同光照下颜色合理变化
- **深度一致性**: 深度关系在各视角下保持正确
- **无视角相关伪影**: 不应出现只在特定视角可见的错误

---

## 📋 第二部分：定量评估指标

### 📊 **1. 数值质量指标**

#### **PSNR (峰值信噪比)**
```
优秀: PSNR > 30 dB
良好: 25 dB < PSNR < 30 dB  
可接受: 20 dB < PSNR < 25 dB
需要改进: PSNR < 20 dB
```

#### **SSIM (结构相似性指数)**
```
优秀: SSIM > 0.95
良好: 0.90 < SSIM < 0.95
可接受: 0.85 < SSIM < 0.90
需要改进: SSIM < 0.85
```

#### **LPIPS (感知损失)**
```
优秀: LPIPS < 0.05
良好: 0.05 < LPIPS < 0.10
可接受: 0.10 < LPIPS < 0.15
需要改进: LPIPS > 0.15
```

### 📊 **2. 深度图质量评估**

#### **深度图特征：**
- **深度连续性**: 相邻像素深度值平滑过渡
- **边界清晰度**: 物体边界处深度跳跃明确
- **深度范围**: 深度值分布合理，覆盖场景范围
- **无空洞**: 深度图应该完整，无大面积空洞

#### **质量检查命令：**
```python
# 检查深度图质量
import numpy as np
import matplotlib.pyplot as plt

depth_path = "outputs/output/stage1_rednet/train/ours_30000/depth_dis/00000.npy"
depth = np.load(depth_path)

print(f"深度范围: {depth.min():.3f} - {depth.max():.3f}")
print(f"深度均值: {depth.mean():.3f}")
print(f"深度标准差: {depth.std():.3f}")
print(f"有效像素比例: {(depth > 0).sum() / depth.size * 100:.2f}%")

# 可视化深度图
plt.figure(figsize=(10, 6))
plt.imshow(depth, cmap='viridis')
plt.colorbar()
plt.title('Depth Map Quality Check')
plt.savefig('depth_quality_check.png')
```

---

## 📋 第三部分：具体检查方法

### 🔍 **1. 视觉检查流程**

#### **步骤1：整体质量检查**
```bash
# 查看训练视角渲染结果
ls outputs/output/stage1_rednet/train/ours_30000/renders/

# 随机检查几张渲染图像
python -c "
import cv2
import numpy as np
from PIL import Image

# 加载原始图像和渲染图像
original = cv2.imread('data/rednet/images/00000.png')
rendered = cv2.imread('outputs/output/stage1_rednet/train/ours_30000/renders/00000.png')

print(f'原始图像尺寸: {original.shape}')
print(f'渲染图像尺寸: {rendered.shape}')

# 计算基本差异
diff = cv2.absdiff(original, rendered)
mean_diff = np.mean(diff)
print(f'平均像素差异: {mean_diff:.2f}')
"
```

#### **步骤2：掩码区域检查**
```python
# 检查掩码区域的渲染质量
import cv2
import numpy as np

# 加载图像和掩码
original = cv2.imread('data/rednet/images/00000.png')
rendered = cv2.imread('outputs/output/stage1_rednet/train/ours_30000/renders/00000.png')
mask = cv2.imread('data/rednet/seg/00000.png', cv2.IMREAD_GRAYSCALE)

# 创建二值掩码（假设掩码值255为需要修复的区域）
binary_mask = (mask == 255).astype(np.uint8)

# 计算掩码区域和背景区域的质量
mask_area = binary_mask.sum()
total_area = binary_mask.size

print(f"掩码区域像素数: {mask_area}")
print(f"掩码区域占比: {mask_area/total_area*100:.2f}%")

# 背景区域质量检查（非掩码区域应该与原图一致）
background_mask = (binary_mask == 0)
bg_diff = cv2.absdiff(original, rendered)
bg_diff_masked = bg_diff[background_mask]
bg_quality = np.mean(bg_diff_masked)

print(f"背景区域平均差异: {bg_quality:.2f}")
if bg_quality < 5.0:
    print("✅ 背景重建质量优秀")
elif bg_quality < 10.0:
    print("⚠️ 背景重建质量良好")
else:
    print("❌ 背景重建质量需要改进")
```

### 🔍 **2. 深度图检查**

#### **深度图完整性检查：**
```python
import numpy as np
import os

depth_dir = "outputs/output/stage1_rednet/train/ours_30000/depth_dis/"
depth_files = [f for f in os.listdir(depth_dir) if f.endswith('.npy')]

print(f"深度图文件数量: {len(depth_files)}")
print(f"预期文件数量: 58 (与图像数量一致)")

# 检查几个深度图的质量
for i, depth_file in enumerate(depth_files[:5]):
    depth_path = os.path.join(depth_dir, depth_file)
    depth = np.load(depth_path)
    
    valid_pixels = (depth > 0).sum()
    total_pixels = depth.size
    coverage = valid_pixels / total_pixels * 100
    
    print(f"{depth_file}: 深度覆盖率 {coverage:.1f}%")
    
    if coverage > 95:
        print(f"  ✅ 深度图完整性优秀")
    elif coverage > 85:
        print(f"  ⚠️ 深度图完整性良好") 
    else:
        print(f"  ❌ 深度图完整性需要改进")
```

### 🔍 **3. 点云质量检查**

#### **点云文件检查：**
```python
from plyfile import PlyData

# 检查最终点云文件
ply_path = "outputs/output/stage1_rednet/point_cloud/iteration_30000/point_cloud.ply"
ply_data = PlyData.read(ply_path)

vertex_data = ply_data['vertex']
num_points = len(vertex_data.data)

print(f"点云点数: {num_points:,}")
print(f"点云属性: {len(vertex_data.properties)}")

# 检查17通道结构
expected_properties = ['x', 'y', 'z', 'nx', 'ny', 'nz', 'f_dc_0', 'f_dc_1', 'f_dc_2', 
                      'opacity', 'scale_0', 'scale_1', 'scale_2', 'rot_0', 'rot_1', 'rot_2', 'rot_3']

actual_properties = [prop.name for prop in vertex_data.properties]
print(f"实际属性: {actual_properties}")

if len(actual_properties) == 17:
    print("✅ 点云结构正确 (17通道)")
else:
    print(f"❌ 点云结构异常 (期望17通道，实际{len(actual_properties)}通道)")

# 检查点云密度
if num_points > 100000:
    print("✅ 点云密度充足")
elif num_points > 50000:
    print("⚠️ 点云密度中等")
else:
    print("❌ 点云密度可能不足")
```

---

## 📋 第四部分：质量问题诊断

### ⚠️ **常见质量问题及解决方案**

#### **1. 渲染模糊或不清晰**
**可能原因：**
- 学习率过高导致训练不稳定
- 点云密度不足
- 训练轮数不够

**解决方案：**
```bash
# 降低学习率重新训练
python train.py ... --position_lr_init 0.00012 --feature_lr 0.002

# 增加训练轮数
python train.py ... --iterations 40000

# 调整密化参数
python train.py ... --densify_grad_threshold 0.0001
```

#### **2. 颜色偏差或失真**
**可能原因：**
- 颜色增强参数不当
- 光照模型不准确
- 球谐特征学习不充分

**解决方案：**
```bash
# 调整颜色增强
python train.py ... --color_aug_prob 0.5

# 增加特征学习率
python train.py ... --feature_lr 0.003
```

#### **3. 掩码边界不自然**
**可能原因：**
- 掩码质量问题
- 掩码训练参数不当
- 边界处点云密度不足

**解决方案：**
```bash
# 检查掩码质量
python -c "
import cv2
import numpy as np
mask = cv2.imread('data/rednet/seg/00000.png', 0)
print(f'掩码唯一值: {np.unique(mask)}')
"

# 调整掩码训练参数
python train.py ... --mask_training --densification_interval 50
```

#### **4. 深度图不连续或有空洞**
**可能原因：**
- 相机参数不准确
- 点云初始化问题
- 训练收敛不充分

**解决方案：**
```bash
# 检查相机参数
python -c "
import json
with open('outputs/output/stage1_rednet/cameras.json', 'r') as f:
    cameras = json.load(f)
print(f'相机数量: {len(cameras)}')
"

# 延长训练时间
python train.py ... --iterations 35000
```

---

## 🎯 第五部分：质量评估总结

### ✅ **优秀质量标准**

#### **视觉质量：**
- 渲染图像清晰锐利，无明显伪影
- 颜色准确，与原图高度一致
- 掩码区域边界自然，无断层
- 多视角一致性良好

#### **数值指标：**
- PSNR > 30 dB
- SSIM > 0.95  
- LPIPS < 0.05
- 深度图覆盖率 > 95%

#### **文件完整性：**
- 点云文件包含17通道，点数 > 100K
- 深度图文件数量与图像数量一致
- 相机参数文件完整

### 📊 **质量评估检查清单**

```
□ 渲染图像视觉质量检查
□ 掩码区域质量验证  
□ 背景重建准确性检查
□ 深度图完整性和连续性验证
□ 点云文件结构和密度检查
□ 数值指标计算 (PSNR/SSIM/LPIPS)
□ 多视角一致性检查
□ 相机参数和输出文件完整性验证
```

### 🎯 **最终建议**

1. **优先检查视觉质量**：人眼观察是最直观的质量评估方法
2. **重点关注掩码区域**：这是Stage 2-3的关键输入
3. **验证深度图质量**：深度信息对后续深度修复至关重要
4. **确保文件完整性**：所有输出文件都是后续阶段的必需输入

**高质量的Stage 1输出是整个CISM流水线成功的基础！** 🎯

---

## 📋 第六部分：自动化质量检查脚本

### 🔧 **完整质量检查脚本**

```python
#!/usr/bin/env python3
"""
Stage 1 渲染质量自动检查脚本
使用方法: python check_stage1_quality.py --output_dir outputs/output/stage1_rednet --data_dir data/rednet
"""

import os
import cv2
import numpy as np
import json
import argparse
from PIL import Image
from plyfile import PlyData
import matplotlib.pyplot as plt

def check_file_completeness(output_dir, data_dir):
    """检查输出文件完整性"""
    print("🔍 检查文件完整性...")

    # 检查关键目录和文件
    required_paths = [
        "point_cloud/iteration_30000/point_cloud.ply",
        "cameras.json",
        "train/ours_30000/renders/",
        "train/ours_30000/depth_dis/"
    ]

    missing_files = []
    for path in required_paths:
        full_path = os.path.join(output_dir, path)
        if not os.path.exists(full_path):
            missing_files.append(path)

    if missing_files:
        print(f"❌ 缺失文件: {missing_files}")
        return False
    else:
        print("✅ 所有必需文件都存在")
        return True

def check_point_cloud_quality(output_dir):
    """检查点云质量"""
    print("\n🔍 检查点云质量...")

    ply_path = os.path.join(output_dir, "point_cloud/iteration_30000/point_cloud.ply")

    try:
        ply_data = PlyData.read(ply_path)
        vertex_data = ply_data['vertex']
        num_points = len(vertex_data.data)
        num_properties = len(vertex_data.properties)

        print(f"点云点数: {num_points:,}")
        print(f"点云属性数: {num_properties}")

        # 检查17通道结构
        if num_properties == 17:
            print("✅ 点云结构正确 (17通道)")
        else:
            print(f"⚠️ 点云结构异常 (期望17通道，实际{num_properties}通道)")

        # 检查点云密度
        if num_points > 100000:
            print("✅ 点云密度充足")
            density_score = "优秀"
        elif num_points > 50000:
            print("⚠️ 点云密度中等")
            density_score = "良好"
        else:
            print("❌ 点云密度可能不足")
            density_score = "需要改进"

        return {
            "num_points": num_points,
            "num_properties": num_properties,
            "density_score": density_score,
            "structure_correct": num_properties == 17
        }

    except Exception as e:
        print(f"❌ 点云文件读取失败: {e}")
        return None

def check_render_quality(output_dir, data_dir):
    """检查渲染质量"""
    print("\n🔍 检查渲染质量...")

    renders_dir = os.path.join(output_dir, "train/ours_30000/renders")
    images_dir = os.path.join(data_dir, "images")

    if not os.path.exists(renders_dir):
        print("❌ 渲染结果目录不存在")
        return None

    render_files = [f for f in os.listdir(renders_dir) if f.endswith('.png')]
    image_files = [f for f in os.listdir(images_dir) if f.endswith('.png')]

    print(f"渲染图像数量: {len(render_files)}")
    print(f"原始图像数量: {len(image_files)}")

    if len(render_files) != len(image_files):
        print("⚠️ 渲染图像数量与原始图像不匹配")

    # 随机选择几张图像进行质量检查
    sample_files = render_files[:min(5, len(render_files))]
    quality_scores = []

    for filename in sample_files:
        try:
            # 加载图像
            original_path = os.path.join(images_dir, filename)
            rendered_path = os.path.join(renders_dir, filename)

            if not os.path.exists(original_path):
                continue

            original = cv2.imread(original_path)
            rendered = cv2.imread(rendered_path)

            if original is None or rendered is None:
                continue

            # 计算PSNR
            mse = np.mean((original.astype(float) - rendered.astype(float)) ** 2)
            if mse == 0:
                psnr = float('inf')
            else:
                psnr = 20 * np.log10(255.0 / np.sqrt(mse))

            # 计算SSIM (简化版本)
            def ssim_simple(img1, img2):
                mu1 = np.mean(img1)
                mu2 = np.mean(img2)
                sigma1 = np.var(img1)
                sigma2 = np.var(img2)
                sigma12 = np.mean((img1 - mu1) * (img2 - mu2))

                c1 = (0.01 * 255) ** 2
                c2 = (0.03 * 255) ** 2

                ssim = ((2 * mu1 * mu2 + c1) * (2 * sigma12 + c2)) / \
                       ((mu1**2 + mu2**2 + c1) * (sigma1 + sigma2 + c2))
                return ssim

            # 转换为灰度图计算SSIM
            gray1 = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(rendered, cv2.COLOR_BGR2GRAY)
            ssim = ssim_simple(gray1, gray2)

            quality_scores.append({
                'filename': filename,
                'psnr': psnr,
                'ssim': ssim
            })

            print(f"{filename}: PSNR={psnr:.2f}dB, SSIM={ssim:.3f}")

        except Exception as e:
            print(f"处理 {filename} 时出错: {e}")

    if quality_scores:
        avg_psnr = np.mean([s['psnr'] for s in quality_scores if s['psnr'] != float('inf')])
        avg_ssim = np.mean([s['ssim'] for s in quality_scores])

        print(f"\n平均PSNR: {avg_psnr:.2f}dB")
        print(f"平均SSIM: {avg_ssim:.3f}")

        # 质量评级
        if avg_psnr > 30 and avg_ssim > 0.95:
            quality_grade = "优秀"
            print("✅ 渲染质量优秀")
        elif avg_psnr > 25 and avg_ssim > 0.90:
            quality_grade = "良好"
            print("⚠️ 渲染质量良好")
        else:
            quality_grade = "需要改进"
            print("❌ 渲染质量需要改进")

        return {
            "avg_psnr": avg_psnr,
            "avg_ssim": avg_ssim,
            "quality_grade": quality_grade,
            "sample_count": len(quality_scores)
        }

    return None

def check_depth_quality(output_dir):
    """检查深度图质量"""
    print("\n🔍 检查深度图质量...")

    depth_dir = os.path.join(output_dir, "train/ours_30000/depth_dis")

    if not os.path.exists(depth_dir):
        print("❌ 深度图目录不存在")
        return None

    depth_files = [f for f in os.listdir(depth_dir) if f.endswith('.npy')]
    print(f"深度图文件数量: {len(depth_files)}")

    if len(depth_files) == 0:
        print("❌ 没有找到深度图文件")
        return None

    # 检查几个深度图的质量
    sample_files = depth_files[:min(5, len(depth_files))]
    depth_stats = []

    for filename in sample_files:
        try:
            depth_path = os.path.join(depth_dir, filename)
            depth = np.load(depth_path)

            valid_pixels = (depth > 0).sum()
            total_pixels = depth.size
            coverage = valid_pixels / total_pixels * 100

            depth_range = depth.max() - depth.min() if valid_pixels > 0 else 0
            depth_mean = depth[depth > 0].mean() if valid_pixels > 0 else 0

            depth_stats.append({
                'filename': filename,
                'coverage': coverage,
                'range': depth_range,
                'mean': depth_mean
            })

            print(f"{filename}: 覆盖率={coverage:.1f}%, 深度范围={depth_range:.3f}")

        except Exception as e:
            print(f"处理 {filename} 时出错: {e}")

    if depth_stats:
        avg_coverage = np.mean([s['coverage'] for s in depth_stats])
        print(f"\n平均深度覆盖率: {avg_coverage:.1f}%")

        if avg_coverage > 95:
            depth_grade = "优秀"
            print("✅ 深度图质量优秀")
        elif avg_coverage > 85:
            depth_grade = "良好"
            print("⚠️ 深度图质量良好")
        else:
            depth_grade = "需要改进"
            print("❌ 深度图质量需要改进")

        return {
            "avg_coverage": avg_coverage,
            "depth_grade": depth_grade,
            "sample_count": len(depth_stats)
        }

    return None

def generate_quality_report(output_dir, data_dir):
    """生成完整的质量报告"""
    print("📊 生成Stage 1质量报告")
    print("=" * 50)

    # 执行各项检查
    file_check = check_file_completeness(output_dir, data_dir)
    point_cloud_check = check_point_cloud_quality(output_dir)
    render_check = check_render_quality(output_dir, data_dir)
    depth_check = check_depth_quality(output_dir)

    # 生成总结报告
    print("\n" + "=" * 50)
    print("📋 质量评估总结")
    print("=" * 50)

    overall_score = 0
    max_score = 4

    if file_check:
        print("✅ 文件完整性: 通过")
        overall_score += 1
    else:
        print("❌ 文件完整性: 失败")

    if point_cloud_check and point_cloud_check['structure_correct']:
        print(f"✅ 点云质量: {point_cloud_check['density_score']}")
        if point_cloud_check['density_score'] == "优秀":
            overall_score += 1
        elif point_cloud_check['density_score'] == "良好":
            overall_score += 0.7
    else:
        print("❌ 点云质量: 失败")

    if render_check:
        print(f"✅ 渲染质量: {render_check['quality_grade']}")
        if render_check['quality_grade'] == "优秀":
            overall_score += 1
        elif render_check['quality_grade'] == "良好":
            overall_score += 0.7
    else:
        print("❌ 渲染质量: 失败")

    if depth_check:
        print(f"✅ 深度图质量: {depth_check['depth_grade']}")
        if depth_check['depth_grade'] == "优秀":
            overall_score += 1
        elif depth_check['depth_grade'] == "良好":
            overall_score += 0.7
    else:
        print("❌ 深度图质量: 失败")

    # 总体评分
    overall_percentage = (overall_score / max_score) * 100
    print(f"\n🎯 总体质量评分: {overall_percentage:.1f}%")

    if overall_percentage >= 90:
        print("🎉 Stage 1训练结果优秀，可以进行Stage 2！")
    elif overall_percentage >= 70:
        print("⚠️ Stage 1训练结果良好，建议检查后进行Stage 2")
    else:
        print("❌ Stage 1训练结果需要改进，建议重新训练")

    return overall_percentage

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Stage 1质量检查工具")
    parser.add_argument("--output_dir", default="outputs/output/stage1_rednet",
                       help="Stage 1输出目录")
    parser.add_argument("--data_dir", default="data/rednet",
                       help="原始数据目录")

    args = parser.parse_args()

    if not os.path.exists(args.output_dir):
        print(f"❌ 输出目录不存在: {args.output_dir}")
        exit(1)

    if not os.path.exists(args.data_dir):
        print(f"❌ 数据目录不存在: {args.data_dir}")
        exit(1)

    # 执行质量检查
    score = generate_quality_report(args.output_dir, args.data_dir)
```

### 🚀 **快速质量检查命令**

```bash
# 保存上述脚本为 check_stage1_quality.py，然后运行：
python check_stage1_quality.py \
    --output_dir /home/<USER>/Infusion-main-cism/outputs/output/stage1_rednet \
    --data_dir /home/<USER>/Infusion-main-cism/data/rednet

# 或者使用默认路径：
python check_stage1_quality.py
```

### 📊 **预期输出示例**

```
📊 生成Stage 1质量报告
==================================================
🔍 检查文件完整性...
✅ 所有必需文件都存在

🔍 检查点云质量...
点云点数: 156,432
点云属性数: 17
✅ 点云结构正确 (17通道)
✅ 点云密度充足

🔍 检查渲染质量...
渲染图像数量: 58
原始图像数量: 58
00000.png: PSNR=32.45dB, SSIM=0.967
00001.png: PSNR=31.23dB, SSIM=0.954
...
平均PSNR: 31.84dB
平均SSIM: 0.961
✅ 渲染质量优秀

🔍 检查深度图质量...
深度图文件数量: 58
00000.npy: 覆盖率=97.2%, 深度范围=2.456
...
平均深度覆盖率: 96.8%
✅ 深度图质量优秀

==================================================
📋 质量评估总结
==================================================
✅ 文件完整性: 通过
✅ 点云质量: 优秀
✅ 渲染质量: 优秀
✅ 深度图质量: 优秀

🎯 总体质量评分: 95.0%
🎉 Stage 1训练结果优秀，可以进行Stage 2！
```

---

## 🎯 最终质量判断标准

### ✅ **可以进行Stage 2的标准**
- **总体评分 ≥ 90%**: 所有指标都达到优秀或良好
- **文件完整性**: 必须通过
- **点云结构**: 必须是17通道
- **渲染质量**: PSNR > 25dB, SSIM > 0.90
- **深度图覆盖率**: > 85%

### ⚠️ **需要检查的标准**
- **总体评分 70-90%**: 大部分指标良好，个别需要改进
- 建议检查具体问题后决定是否进行Stage 2

### ❌ **需要重新训练的标准**
- **总体评分 < 70%**: 多个关键指标不达标
- 文件不完整或点云结构错误
- 渲染质量严重不足

**使用这个自动化检查工具，您可以快速准确地评估Stage 1的训练质量！** 🎯
