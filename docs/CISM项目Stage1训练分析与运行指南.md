# 📊 CISM项目Stage 1训练分析与运行指南

## 🎯 数据集分析

### **RedNet数据集详细信息**

#### **数据集结构**
```
/home/<USER>/Infusion-main-cism/data/rednet/
├── images/          # RGB图像数据 (58张)
│   ├── 00000.png    # 1008×576像素，RGB格式
│   ├── 00001.png    # 平均文件大小：~1.2MB
│   └── ...          # 总计58张图像
├── seg/             # 分割掩码数据 (58张)
│   ├── 00000.png    # 1008×576像素，灰度格式
│   ├── 00001.png    # 掩码区域约占12.12%
│   └── ...          # 与images一一对应
└── sparse/          # COLMAP重建数据
    └── 0/
        ├── cameras.bin    # 相机参数
        ├── images.bin     # 图像信息
        ├── points3D.bin   # 3D点云
        └── points3D.ply   # 初始点云文件
```

#### **数据集特征分析**
- **图像数量**: 58张多视角图像
- **图像分辨率**: 1008×576 (中等分辨率)
- **图像格式**: PNG，RGB模式
- **掩码质量**: 精确的语义分割掩码，掩码区域占比约12%
- **场景类型**: RedNet场景（可能是室内或特定物体场景）
- **COLMAP数据**: 完整的相机参数和初始点云

---

## 🖥️ GPU配置分析

### **当前GPU状态**
```
系统配置：8×NVIDIA RTX A6000 (49GB显存/卡)
当前使用情况：
├── GPU 0: 15.6GB/49.1GB (31.7%使用) - 中等负载
├── GPU 1: 13.2GB/49.1GB (26.9%使用) - 中等负载  
├── GPU 2: 36.3GB/49.1GB (73.9%使用) - 高负载 ❌
├── GPU 3: 6.7GB/49.1GB  (13.6%使用) - 低负载 ✅
├── GPU 4: 0.004GB/49.1GB (0.01%使用) - 空闲 ✅
├── GPU 5: 0.006GB/49.1GB (0.01%使用) - 空闲 ✅
├── GPU 6: 22.4GB/49.1GB (45.6%使用) - 中等负载
└── GPU 7: 39.1GB/49.1GB (79.6%使用) - 高负载 ❌
```

### **最佳GPU选择分析**

#### **推荐GPU：GPU 4 或 GPU 5** ✅
**理由：**
- **显存充足**: 49GB显存，当前几乎空闲
- **无竞争**: 没有其他进程占用
- **性能稳定**: RTX A6000专业级GPU，适合长时间训练

#### **备选GPU：GPU 3** ⚠️
**理由：**
- **显存可用**: 42.4GB可用显存
- **轻度使用**: 当前负载较低
- **可接受**: 如果主选GPU出现问题的备选

#### **不推荐GPU：GPU 0, 1, 2, 6, 7** ❌
**理由：**
- GPU 2, 7: 显存使用率过高（>70%）
- GPU 0, 1, 6: 中等负载，可能影响训练稳定性

---

## 🚀 Stage 1训练配置

### **内存需求估算**

#### **基于数据集特征的估算**
```
图像数据: 58张 × 1008×576 × 3通道 × 4字节 ≈ 390MB
高斯点云: 预估100K-500K点 × 17通道 × 4字节 ≈ 6.8-34GB
训练缓存: 梯度、优化器状态等 ≈ 2-4GB
渲染缓冲: 实时渲染所需 ≈ 1-2GB
总计预估: 10-40GB (取决于点云密度)
```

#### **RTX A6000适配性**
- ✅ **显存充足**: 49GB >> 40GB预估需求
- ✅ **性能匹配**: 专业级GPU，适合3D高斯溅射
- ✅ **稳定性**: 长时间训练稳定性好

### **训练参数优化**

#### **基于数据集的参数调整**
```python
# 针对RedNet数据集的优化参数
--iterations 30000          # 标准训练轮数
--position_lr_init 0.00016   # 位置学习率
--feature_lr 0.0025         # 特征学习率  
--opacity_lr 0.05           # 透明度学习率
--scaling_lr 0.005          # 缩放学习率
--rotation_lr 0.001         # 旋转学习率
--densify_grad_threshold 0.0002  # 密化梯度阈值
--densification_interval 100     # 密化间隔
--opacity_reset_interval 3000    # 透明度重置间隔
```

#### **掩码训练特殊配置**
```python
--mask_training             # 启用掩码引导训练
--color_aug                 # 颜色增强（提高深度图可靠性）
```

---

## 📋 完整运行命令

### **方案1：使用GPU 4（推荐）**

```bash
# 设置环境变量
export CUDA_VISIBLE_DEVICES=4

# 进入gaussian_splatting目录
cd /home/<USER>/Infusion-main-cism/gaussian_splatting

# 执行Stage 1训练
python train.py \
    -s /home/<USER>/Infusion-main-cism/data/rednet \
    -m /home/<USER>/Infusion-main-cism/outputs/output/stage1_rednet \
    -u nothing \
    --mask_training \
    --color_aug \
    --iterations 30000 \
    --position_lr_init 0.00016 \
    --feature_lr 0.0025 \
    --opacity_lr 0.05 \
    --scaling_lr 0.005 \
    --rotation_lr 0.001 \
    --densify_grad_threshold 0.0002 \
    --densification_interval 100 \
    --opacity_reset_interval 3000 \
    --save_iterations 7000 30000
```

### **方案2：使用GPU 5（备选）**

```bash
# 设置环境变量
export CUDA_VISIBLE_DEVICES=5

# 其余命令与方案1相同
cd /home/<USER>/Infusion-main-cism/gaussian_splatting
python train.py \
    -s /home/<USER>/Infusion-main-cism/data/rednet \
    -m /home/<USER>/Infusion-main-cism/outputs/output/stage1_rednet \
    -u nothing \
    --mask_training \
    --color_aug \
    --iterations 30000
```

### **训练后渲染命令**

```bash
# 训练完成后执行渲染
python render.py \
    -s /home/<USER>/Infusion-main-cism/data/rednet \
    -m /home/<USER>/Infusion-main-cism/outputs/output/stage1_rednet \
    -u nothing \
    --iteration 30000
```

---

## 📊 预期输出结构

### **训练完成后的输出目录**
```
/home/<USER>/Infusion-main-cism/outputs/output/stage1_rednet/
├── cameras.json                    # 相机参数文件
├── cfg_args                        # 配置参数记录
├── input.ply                       # 输入点云
├── point_cloud/                    # 点云输出目录
│   ├── iteration_7000/
│   │   └── point_cloud.ply         # 中间检查点
│   ├── iteration_30000/
│   │   └── point_cloud.ply         # 最终训练结果 ⭐
│   └── iteration_30001/
│       └── point_cloud.ply         # 最终保存点云
├── train/                          # 训练视角渲染结果
│   └── ours_30000/
│       ├── renders/                # RGB渲染图像
│       ├── depth_dis/              # 深度图 (.npy格式)
│       ├── c2w/                    # 相机到世界矩阵
│       └── intri/                  # 内参矩阵
└── test/                           # 测试视角渲染结果
    └── ours_30000/
        └── renders/                # 测试渲染图像
```

### **关键输出文件说明**
- **point_cloud.ply**: 17通道高斯点云，Stage 2的输入
- **depth_dis/*.npy**: 深度图，用于Stage 2的深度修复
- **c2w/*.npy**: 相机外参，Stage 2需要
- **intri/*.npy**: 相机内参，Stage 2需要
- **renders/*.png**: 渲染图像，用于质量评估

---

## ⚠️ 训练监控与故障排除

### **训练状态监控**
```bash
# 监控GPU使用情况
watch -n 5 nvidia-smi

# 监控训练日志
tail -f /home/<USER>/Infusion-main-cism/outputs/output/stage1_rednet/train.log

# 检查输出文件
ls -la /home/<USER>/Infusion-main-cism/outputs/output/stage1_rednet/point_cloud/
```

### **常见问题与解决方案**

#### **1. CUDA内存不足**
```bash
# 解决方案：降低分辨率或减少批次大小
python train.py ... --resolution 2  # 降低分辨率
```

#### **2. 训练收敛缓慢**
```bash
# 解决方案：检查掩码质量，调整学习率
python train.py ... --position_lr_init 0.0002  # 提高学习率
```

#### **3. 深度图质量差**
```bash
# 解决方案：启用颜色增强
python train.py ... --color_aug  # 已包含在推荐命令中
```

### **预期训练时间**
- **数据集规模**: 58张图像，中等复杂度
- **预估时间**: 2-4小时（RTX A6000）
- **检查点**: 7000和30000迭代自动保存

---

## 🎯 最终建议

### **推荐执行方案**
1. **使用GPU 4**：显存充足，无竞争，最稳定
2. **启用掩码训练**：`--mask_training`确保精确修复
3. **启用颜色增强**：`--color_aug`提高深度图质量
4. **标准参数配置**：使用经过验证的参数设置

### **执行步骤**
```bash
# 1. 检查环境
conda activate infusion_cism_final

# 2. 设置GPU
export CUDA_VISIBLE_DEVICES=4

# 3. 执行训练
cd /home/<USER>/Infusion-main-cism/gaussian_splatting
python train.py -s /home/<USER>/Infusion-main-cism/data/rednet -m /home/<USER>/Infusion-main-cism/outputs/output/stage1_rednet -u nothing --mask_training --color_aug

# 4. 执行渲染
python render.py -s /home/<USER>/Infusion-main-cism/data/rednet -m /home/<USER>/Infusion-main-cism/outputs/output/stage1_rednet -u nothing
```

**准备就绪！可以开始Stage 1训练了！** 🚀
