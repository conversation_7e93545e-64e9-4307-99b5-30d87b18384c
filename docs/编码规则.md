# 简单优雅编码规则

**核心理念：简单是最高的优雅**

## 核心决策规则

### 🔥 KISS原则
**Keep It Simple, Stupid** - 永远选择最简单的解决方案

### 🚦 三秒规则
**如果看代码超过3秒还不知道它在做什么，就需要重构**

### ⚡ 优先级原则
1. **函数 > 类**：除非真的需要状态管理或多态
2. **直接调用 > 管理器**：除非需要协调复杂资源
3. **直接实现 > 抽象**：除非确定需要多种实现

## 结构设计规则

### 文件组织
- 从单文件开始，必要时才拆分
- 工具函数按功能分类
- 不超过3层目录结构
- 不创建只有一个函数的文件

### 函数 vs 类决策
**优先使用函数（90%情况）**，除非需要：
- **维护状态**：数据库连接、缓存
- **多态行为**：不同数据导出格式
- **生命周期管理**：资源创建和清理

## 函数编写规则

### 长度规则
- **理想长度**：5-15行
- **最大长度**：50行
- **超过50行**：必须拆分

### 命名规则
- **动词开头**：`process_data`, `calculate_score`, `save_file`
- **见名知意**：不需要看实现就知道功能
- **避免缩写**：`calculate` 而不是 `calc`

### 参数规则
- **最多5个参数**：超过则使用字典或配置对象
- **避免布尔参数**：用枚举或分离函数

## 数据处理规则

### 优先使用纯函数
- 无副作用，相同输入产生相同输出
- 不修改全局状态或输入参数

### 数据流水线模式
- 将复杂处理分解为独立步骤
- 每个步骤都是纯函数
- 清晰的数据流向

## 深度学习特定规则

### 模型管理
- 使用上下文管理器管理GPU资源
- 自动设备选择和内存清理
- 模型生命周期管理

### GPU内存管理
- 批处理时定期清理缓存
- 推理时禁用梯度计算
- 内存不足时自动降级处理

### 配置管理
- 使用dataclass定义配置
- 配置验证和默认值
- 简单的JSON序列化

### 数据处理
- 分块加载大型数据集
- 内存友好的数据流水线
- 支持多种数据格式

## 性能与简洁性权衡

### 权衡原则
- **80/20法则**：80%时间用简洁实现，20%时间优化关键路径
- **测量驱动**：先测量，再优化
- **渐进式优化**：简单实现 → 发现问题 → 针对性优化

### 何时优先性能
- 计算密集型任务
- 大规模数据处理
- 频繁调用的函数

### 何时优先简洁
- 配置读取
- 一次性操作
- 非关键路径代码

## 错误处理规则

### 简单直接的错误处理
- 针对具体错误类型处理
- 提供有意义的错误信息
- 避免过度复杂的错误处理架构

### 深度学习特定错误处理
- **GPU内存不足**：自动清理缓存，减少批次大小
- **模型加载失败**：检查文件存在性和结构匹配
- **训练中断**：安全保存检查点，原子性操作

### 快速失败原则
- 函数开始就验证参数
- 提前返回，减少嵌套
- 明确的错误边界

## 代码组织规则

### 导入规则
- 导入顺序：标准库 → 第三方库 → 本地模块
- 只导入需要的内容
- 避免 `import *`

### 常量和配置规则
- 常量在文件顶部，全大写
- 配置使用简单字典或dataclass
- 避免复杂的配置类层次

## 测试规则

### 简单直接的测试
- 准备数据 → 执行函数 → 验证结果
- 每个测试函数只测试一个功能点
- 使用清晰的断言

### 测试文件组织
- 按模块组织测试文件
- 测试文件名以 `test_` 开头
- 保持测试结构简单

## 质量检查清单

### 每次提交前检查
**基础检查**
- [ ] 5分钟规则：每个文件能在5分钟内理解
- [ ] 函数长度：没有超过50行的函数
- [ ] 嵌套深度：没有超过3层的if/for嵌套
- [ ] 命名清晰：所有函数和变量见名知意
- [ ] 无重复代码：相同逻辑没有重复实现

**深度学习特定检查**
- [ ] GPU内存管理：有适当的内存清理和错误处理
- [ ] 模型状态管理：模型加载/保存有错误处理
- [ ] 批处理安全：大数据处理有内存保护
- [ ] 设备兼容性：代码在CPU/GPU环境都能运行

### 定期检查
**每周**：文件数量、函数数量、依赖审查、性能检查
**每月**：简洁性评估、技术债务、文档更新、最佳实践总结

## 实战示例

### 深度学习训练脚本
```python
# 简化的训练主函数
def train_model(config):
    model, optimizer, criterion, device = setup_training(config)
    train_loader = create_data_loader(config.train_data_path, config.batch_size)

    for epoch in range(config.num_epochs):
        train_loss = train_one_epoch(model, train_loader, optimizer, criterion, device)
        val_loss = validate_model(model, val_loader, criterion, device)

        if val_loss < best_val_loss:
            save_checkpoint(model, optimizer, epoch, val_loss, config.checkpoint_path)
```

### 工具函数示例
```python
# 简单的文件操作工具
def load_json(filename):
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return None
    except json.JSONDecodeError:
        return None

def save_json(data, filename):
    Path(filename).parent.mkdir(parents=True, exist_ok=True)
    with open(filename, 'w') as f:
        json.dump(data, f, indent=2)
```

## 成功标准

### 通用标准
1. **新人友好**：同事能在30分钟内理解整个模块
2. **修改容易**：改一个功能通常只需修改一个函数
3. **调试简单**：出问题能快速定位到具体位置
4. **测试直接**：每个函数都容易写测试
5. **部署轻松**：依赖少，配置简单

### 深度学习项目特定标准
6. **资源安全**：GPU内存使用合理，有适当的清理机制
7. **实验可复现**：相同配置能得到相同结果
8. **训练稳定**：长时间训练不会因为内存或其他问题中断
9. **模型管理清晰**：模型加载、保存、版本管理简单明了
10. **配置灵活**：超参数调整不需要修改代码

### 代码质量指标
- **行数控制**：单个函数 < 50行，单个文件 < 500行
- **复杂度控制**：嵌套层级 < 3层，函数参数 < 5个
- **依赖最小化**：只使用必要的第三方库
- **错误处理完善**：关键操作都有适当的异常处理

## 决策树参考

### 函数 vs 类
```
需要维护状态？ → 是：考虑使用类 / 否：使用函数
需要多态？ → 是：使用类 / 否：使用函数
需要封装复杂逻辑？ → 是：考虑使用类 / 否：使用函数
```

### 直接调用 vs 管理器
```
需要协调多个复杂资源？ → 是：考虑使用管理器 / 否：直接调用
需要生命周期管理？ → 是：考虑使用管理器 / 否：直接调用
```

### 抽象 vs 直接实现
```
需要支持多种实现？ → 是：考虑抽象 / 否：直接实现
确定会扩展？ → 是：考虑抽象 / 否：直接实现（YAGNI原则）
```

---

**记住：简单是最高的优雅。当你面临选择时，永远选择更简单的方案。**

**对于深度学习项目：先让它工作，再让它正确，最后让它快速。永远不要跳过前两步直接优化性能。**
