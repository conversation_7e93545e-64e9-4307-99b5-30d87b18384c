# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-08-17 23:24:00 - 初始化记忆银行，基于README.md分析项目上下文

## Project Goal

InFusion-CISM 是一个基于InFusion框架的高质量3D场景修复系统，用于文本到4D生成和语义感知的3D场景修复。项目采用CISM（Concept-aware Inpainting with Interval Score Matching）算法，解决传统SDS过平滑问题，在修复区域生成更丰富的细节。

## Key Features

- **四阶段工作流程**: 几何合并 → 概念注入 → 监督学习微调 → CISM训练
- **精确的空间概念分配**: 继承Stage 1的相机参数进行精确的3D到2D投影
- **ISM算法**: 解决传统SDS过平滑问题，生成丰富细节
- **概念感知训练**: 精确保护背景区域，只优化修复区域
- **简化的代码架构**: 移除复杂抽象，采用直接实现方式
- **完全兼容**: 与InFusion原始项目100%兼容

## Overall Architecture

### 核心组件架构
- **Stage 1-2 (InFusion原始)**: 空间生成和几何处理 - 禁止修改区域
- **Stage 2.6**: 几何合并 (`stage2_6_workflow.py`)
- **Stage 2.7**: 概念注入 (`spatial_concept_assigner.py`)  
- **Stage 2.8**: 监督学习微调 (`stage2_8_supervision.py`)
- **Stage 3**: CISM训练 (`cism_stage3.py`)
- **验证**: 渲染和验证 (`render_merged_concepts.py`)

### 技术栈
- **3D处理**: Gaussian Splatting, Point Cloud处理
- **深度学习**: Stable Diffusion 2.1, ISM算法
- **概念处理**: 空间概念分配，掩码引导编辑
- **环境**: micromamba, infusion_cism_final环境

### 数据流
```
InFusion Stage1/2 → 几何合并 → 概念注入 → 监督微调 → CISM训练 → 最终输出
bg.ply + repair.ply → merged_*.ply → merged_*_with_concepts.ply → *_supervised.ply → final_merged.ply