#!/usr/bin/env python3
"""
深度图质量详细分析脚本
分析Stage 1生成的深度图质量，判断是否适合进入Stage 2
"""

import numpy as np
import matplotlib.pyplot as plt
import cv2
import os
from PIL import Image
import argparse

def analyze_single_depth(depth_path, save_visualization=True):
    """分析单个深度图的质量"""
    print(f"\n🔍 分析深度图: {os.path.basename(depth_path)}")
    
    # 加载深度图
    depth = np.load(depth_path)
    
    # 基本统计信息
    print(f"深度图尺寸: {depth.shape}")
    print(f"深度值范围: {depth.min():.6f} - {depth.max():.6f}")
    print(f"深度均值: {depth.mean():.6f}")
    print(f"深度标准差: {depth.std():.6f}")
    
    # 有效性检查
    valid_pixels = depth > 0
    valid_count = valid_pixels.sum()
    total_pixels = depth.size
    coverage = valid_count / total_pixels * 100
    
    print(f"有效像素覆盖率: {coverage:.2f}%")
    print(f"零值像素数: {(depth == 0).sum()}")
    print(f"无穷大值数: {np.isinf(depth).sum()}")
    print(f"NaN值数: {np.isnan(depth).sum()}")
    
    # 深度连续性分析
    if valid_count > 0:
        valid_depth = depth[valid_pixels]
        
        # 深度分布
        percentiles = [5, 25, 50, 75, 95]
        print(f"深度值分布:")
        for p in percentiles:
            print(f"  {p}%分位数: {np.percentile(valid_depth, p):.6f}")
        
        # 深度梯度分析（连续性检查）
        grad_x = np.abs(np.gradient(depth, axis=1))
        grad_y = np.abs(np.gradient(depth, axis=0))
        grad_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        
        # 只考虑有效区域的梯度
        valid_grad = grad_magnitude[valid_pixels]
        avg_gradient = valid_grad.mean()
        max_gradient = valid_grad.max()
        
        print(f"深度连续性分析:")
        print(f"  平均梯度: {avg_gradient:.6f}")
        print(f"  最大梯度: {max_gradient:.6f}")
        print(f"  高梯度像素比例: {(valid_grad > avg_gradient * 3).sum() / len(valid_grad) * 100:.2f}%")
        
        # 质量评级
        quality_score = 0
        quality_issues = []
        
        # 覆盖率评分 (40分)
        if coverage >= 95:
            quality_score += 40
        elif coverage >= 90:
            quality_score += 35
            quality_issues.append("覆盖率略低")
        elif coverage >= 80:
            quality_score += 25
            quality_issues.append("覆盖率偏低")
        else:
            quality_score += 10
            quality_issues.append("覆盖率严重不足")
        
        # 深度范围评分 (30分)
        depth_range = valid_depth.max() - valid_depth.min()
        if depth_range > 0.5:
            quality_score += 30
        elif depth_range > 0.3:
            quality_score += 25
            quality_issues.append("深度范围较小")
        elif depth_range > 0.1:
            quality_score += 15
            quality_issues.append("深度范围很小")
        else:
            quality_score += 5
            quality_issues.append("深度范围过小")
        
        # 连续性评分 (30分)
        if avg_gradient < 0.01:
            quality_score += 30
        elif avg_gradient < 0.02:
            quality_score += 25
            quality_issues.append("深度变化略大")
        elif avg_gradient < 0.05:
            quality_score += 15
            quality_issues.append("深度不够平滑")
        else:
            quality_score += 5
            quality_issues.append("深度变化过大")
        
        print(f"\n📊 质量评分: {quality_score}/100")
        
        if quality_score >= 90:
            quality_level = "优秀"
            recommendation = "✅ 深度图质量优秀，可以直接进入Stage 2"
        elif quality_score >= 75:
            quality_level = "良好"
            recommendation = "⚠️ 深度图质量良好，建议进入Stage 2"
        elif quality_score >= 60:
            quality_level = "一般"
            recommendation = "⚠️ 深度图质量一般，可能需要调整参数重新训练"
        else:
            quality_level = "较差"
            recommendation = "❌ 深度图质量较差，建议重新训练Stage 1"
        
        print(f"质量等级: {quality_level}")
        print(f"建议: {recommendation}")
        
        if quality_issues:
            print(f"发现的问题: {', '.join(quality_issues)}")
        
        # 可视化
        if save_visualization:
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            
            # 原始深度图
            im1 = axes[0,0].imshow(depth, cmap='viridis')
            axes[0,0].set_title('原始深度图')
            axes[0,0].axis('off')
            plt.colorbar(im1, ax=axes[0,0])
            
            # 深度直方图
            axes[0,1].hist(valid_depth, bins=50, alpha=0.7, color='blue')
            axes[0,1].set_title('深度值分布')
            axes[0,1].set_xlabel('深度值')
            axes[0,1].set_ylabel('像素数量')
            
            # 梯度图
            im3 = axes[1,0].imshow(grad_magnitude, cmap='hot')
            axes[1,0].set_title('深度梯度')
            axes[1,0].axis('off')
            plt.colorbar(im3, ax=axes[1,0])
            
            # 质量评分可视化
            axes[1,1].bar(['覆盖率', '深度范围', '连续性', '总分'], 
                         [min(coverage, 100), depth_range*100, (1-avg_gradient)*100, quality_score],
                         color=['green', 'blue', 'orange', 'red'])
            axes[1,1].set_title('质量指标')
            axes[1,1].set_ylabel('分数')
            axes[1,1].set_ylim(0, 100)
            
            plt.tight_layout()
            
            # 保存可视化结果
            output_name = os.path.basename(depth_path).replace('.npy', '_analysis.png')
            plt.savefig(output_name, dpi=150, bbox_inches='tight')
            print(f"可视化结果已保存: {output_name}")
            plt.close()
        
        return {
            'quality_score': quality_score,
            'quality_level': quality_level,
            'coverage': coverage,
            'depth_range': depth_range,
            'avg_gradient': avg_gradient,
            'issues': quality_issues,
            'recommendation': recommendation
        }
    
    else:
        print("❌ 深度图无有效像素")
        return None

def analyze_depth_directory(depth_dir, sample_count=5):
    """分析整个深度图目录"""
    print(f"\n🔍 分析深度图目录: {depth_dir}")
    
    depth_files = [f for f in os.listdir(depth_dir) if f.endswith('.npy')]
    print(f"找到 {len(depth_files)} 个深度图文件")
    
    if len(depth_files) == 0:
        print("❌ 未找到深度图文件")
        return None
    
    # 随机采样分析
    sample_files = np.random.choice(depth_files, min(sample_count, len(depth_files)), replace=False)
    
    results = []
    for filename in sample_files:
        depth_path = os.path.join(depth_dir, filename)
        result = analyze_single_depth(depth_path, save_visualization=False)
        if result:
            result['filename'] = filename
            results.append(result)
    
    if results:
        # 统计分析
        scores = [r['quality_score'] for r in results]
        coverages = [r['coverage'] for r in results]
        
        print(f"\n📊 整体质量分析 (基于{len(results)}个样本):")
        print(f"平均质量分数: {np.mean(scores):.1f}/100")
        print(f"最低质量分数: {np.min(scores):.1f}/100")
        print(f"平均覆盖率: {np.mean(coverages):.1f}%")
        
        # 整体建议
        avg_score = np.mean(scores)
        min_score = np.min(scores)
        
        if avg_score >= 85 and min_score >= 70:
            overall_recommendation = "✅ 整体深度图质量优秀，强烈建议进入Stage 2"
        elif avg_score >= 75 and min_score >= 60:
            overall_recommendation = "⚠️ 整体深度图质量良好，建议进入Stage 2"
        elif avg_score >= 65:
            overall_recommendation = "⚠️ 整体深度图质量一般，可以尝试Stage 2，但可能需要调整参数"
        else:
            overall_recommendation = "❌ 整体深度图质量较差，建议重新训练Stage 1"
        
        print(f"整体建议: {overall_recommendation}")
        
        return {
            'avg_score': avg_score,
            'min_score': min_score,
            'avg_coverage': np.mean(coverages),
            'recommendation': overall_recommendation,
            'sample_results': results
        }
    
    return None

def main():
    parser = argparse.ArgumentParser(description='深度图质量分析')
    parser.add_argument('--depth_dir', type=str, help='深度图目录路径')
    parser.add_argument('--single_file', type=str, help='分析单个深度图文件')
    parser.add_argument('--sample_count', type=int, default=5, help='采样分析的文件数量')

    args = parser.parse_args()

    if args.single_file:
        analyze_single_depth(args.single_file)
    elif args.depth_dir:
        analyze_depth_directory(args.depth_dir, args.sample_count)
    else:
        print("请指定 --depth_dir 或 --single_file 参数")
        parser.print_help()

if __name__ == "__main__":
    main()
