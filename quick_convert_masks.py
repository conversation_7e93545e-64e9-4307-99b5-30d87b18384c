#!/usr/bin/env python3
"""
快速掩码转换脚本 - 将RedNet语义掩码转换为InFusion二值掩码

推荐使用策略A：将所有非背景区域转换为修复区域
这是最安全和通用的转换方式
"""

import cv2
import numpy as np
import os
import shutil
from pathlib import Path

def convert_rednet_to_binary_masks():
    """将RedNet掩码转换为二值掩码"""
    
    print("🎭 RedNet掩码快速转换工具")
    print("=" * 50)
    
    # 路径设置
    input_dir = "data/rednet/seg"
    output_dir = "data/rednet/seg_binary"
    backup_dir = "data/rednet/seg_original_backup"
    
    # 检查输入目录
    if not os.path.exists(input_dir):
        print(f"❌ 输入目录不存在: {input_dir}")
        return False
    
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # 备份原始掩码（可选）
    print(f"📁 创建原始掩码备份...")
    if os.path.exists(backup_dir):
        print(f"⚠️ 备份目录已存在: {backup_dir}")
    else:
        shutil.copytree(input_dir, backup_dir)
        print(f"✅ 原始掩码已备份到: {backup_dir}")
    
    # 获取所有掩码文件
    mask_files = [f for f in os.listdir(input_dir) if f.endswith('.png')]
    mask_files.sort()
    
    if len(mask_files) == 0:
        print(f"❌ 在 {input_dir} 中没有找到PNG掩码文件")
        return False
    
    print(f"\n🔄 开始转换 {len(mask_files)} 个掩码文件...")
    print("转换策略: 所有非背景区域(值!=0) → 修复区域(255)")
    
    # 转换统计
    successful = 0
    failed = 0
    total_mask_pixels = 0
    total_pixels = 0
    
    # 预览前3个文件的转换效果
    print("\n🔍 转换效果预览:")
    for i, filename in enumerate(mask_files[:3]):
        input_path = os.path.join(input_dir, filename)
        mask = cv2.imread(input_path, cv2.IMREAD_GRAYSCALE)
        
        if mask is not None:
            original_classes = len(np.unique(mask))
            binary_mask = np.zeros_like(mask)
            binary_mask[mask != 0] = 255
            mask_ratio = np.sum(binary_mask == 255) / binary_mask.size * 100
            
            print(f"  {filename}: {original_classes}类别 → 修复区域{mask_ratio:.1f}%")
    
    # 确认转换
    print(f"\n📋 转换设置:")
    print(f"输入目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    print(f"备份目录: {backup_dir}")
    print(f"文件数量: {len(mask_files)}")
    
    confirm = input("\n是否继续转换? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 转换已取消")
        return False
    
    # 执行转换
    print(f"\n🚀 开始转换...")
    
    for i, filename in enumerate(mask_files):
        try:
            input_path = os.path.join(input_dir, filename)
            output_path = os.path.join(output_dir, filename)
            
            # 读取原始掩码
            mask = cv2.imread(input_path, cv2.IMREAD_GRAYSCALE)
            if mask is None:
                print(f"❌ 无法读取: {filename}")
                failed += 1
                continue
            
            # 转换为二值掩码
            binary_mask = np.zeros_like(mask)
            binary_mask[mask != 0] = 255  # 所有非0值变成255
            
            # 保存转换后的掩码
            cv2.imwrite(output_path, binary_mask)
            
            # 统计信息
            mask_pixels = np.sum(binary_mask == 255)
            total_mask_pixels += mask_pixels
            total_pixels += binary_mask.size
            successful += 1
            
            # 显示进度
            if (i + 1) % 10 == 0 or i == 0:
                mask_ratio = mask_pixels / binary_mask.size * 100
                print(f"  {i+1:3d}/{len(mask_files)} - {filename}: 修复区域 {mask_ratio:.1f}%")
                
        except Exception as e:
            print(f"❌ 转换失败 {filename}: {e}")
            failed += 1
    
    # 转换完成统计
    overall_mask_ratio = total_mask_pixels / total_pixels * 100 if total_pixels > 0 else 0
    
    print(f"\n✅ 转换完成!")
    print(f"成功转换: {successful} 个文件")
    print(f"转换失败: {failed} 个文件")
    print(f"平均修复区域占比: {overall_mask_ratio:.2f}%")
    
    # 验证转换结果
    print(f"\n🔍 验证转换结果...")
    sample_file = mask_files[0]
    original_path = os.path.join(input_dir, sample_file)
    converted_path = os.path.join(output_dir, sample_file)
    
    original = cv2.imread(original_path, cv2.IMREAD_GRAYSCALE)
    converted = cv2.imread(converted_path, cv2.IMREAD_GRAYSCALE)
    
    if original is not None and converted is not None:
        orig_unique = np.unique(original)
        conv_unique = np.unique(converted)
        
        print(f"验证文件: {sample_file}")
        print(f"  原始掩码值: {orig_unique[:10]}... (共{len(orig_unique)}个)")
        print(f"  转换掩码值: {conv_unique} (共{len(conv_unique)}个)")
        
        if len(conv_unique) <= 2 and 0 in conv_unique:
            print("  ✅ 转换格式正确 (二值掩码)")
        else:
            print("  ❌ 转换格式异常")
    
    return successful > 0

def create_rednet_binary_dataset():
    """创建使用二值掩码的RedNet数据集副本"""
    
    print(f"\n📁 创建RedNet二值掩码数据集...")
    
    # 路径设置
    original_data_dir = "data/rednet"
    binary_data_dir = "data/rednet_binary"
    
    # 创建新数据集目录
    Path(binary_data_dir).mkdir(parents=True, exist_ok=True)
    
    # 复制images和sparse目录
    for subdir in ['images', 'sparse']:
        src_dir = os.path.join(original_data_dir, subdir)
        dst_dir = os.path.join(binary_data_dir, subdir)
        
        if os.path.exists(src_dir):
            if os.path.exists(dst_dir):
                shutil.rmtree(dst_dir)
            shutil.copytree(src_dir, dst_dir)
            print(f"  ✅ 复制 {subdir} 目录")
    
    # 复制转换后的掩码
    src_seg_dir = "data/rednet/seg_binary"
    dst_seg_dir = os.path.join(binary_data_dir, "seg")
    
    if os.path.exists(src_seg_dir):
        if os.path.exists(dst_seg_dir):
            shutil.rmtree(dst_seg_dir)
        shutil.copytree(src_seg_dir, dst_seg_dir)
        print(f"  ✅ 复制二值掩码到 seg 目录")
    else:
        print(f"  ❌ 二值掩码目录不存在: {src_seg_dir}")
        return False
    
    print(f"✅ RedNet二值掩码数据集创建完成: {binary_data_dir}")
    
    # 验证数据集结构
    required_dirs = ['images', 'seg', 'sparse']
    for req_dir in required_dirs:
        full_path = os.path.join(binary_data_dir, req_dir)
        if os.path.exists(full_path):
            file_count = len([f for f in os.listdir(full_path) if not os.path.isdir(os.path.join(full_path, f))])
            print(f"  📁 {req_dir}: {file_count} 个文件")
        else:
            print(f"  ❌ 缺失目录: {req_dir}")
            return False
    
    return True

def main():
    """主函数"""
    
    # 步骤1: 转换掩码
    print("🎯 步骤1: 转换掩码格式")
    success = convert_rednet_to_binary_masks()
    
    if not success:
        print("❌ 掩码转换失败，程序退出")
        return
    
    # 步骤2: 创建新数据集
    print("\n🎯 步骤2: 创建二值掩码数据集")
    dataset_success = create_rednet_binary_dataset()
    
    if dataset_success:
        print(f"\n🎉 转换完成! 下一步操作:")
        print(f"1. 检查转换结果:")
        print(f"   ls data/rednet_binary/")
        print(f"   python -c \"import cv2; import numpy as np; mask=cv2.imread('data/rednet_binary/seg/00000.png', 0); print('掩码值:', np.unique(mask))\"")
        print(f"")
        print(f"2. 使用新数据集重新训练Stage 1:")
        print(f"   cd gaussian_splatting")
        print(f"   python train.py -s ../data/rednet_binary -m ../outputs/output/stage1_rednet_binary --mask_training --color_aug")
        print(f"")
        print(f"3. 对比训练效果:")
        print(f"   原始语义掩码: outputs/output/stage1_rednet")
        print(f"   二值掩码: outputs/output/stage1_rednet_binary")
    else:
        print("❌ 数据集创建失败")

if __name__ == "__main__":
    main()
