#!/usr/bin/env python3
"""
简化的Grounded SAM测试脚本
用于为 00000.png 创建掩码
"""

import os
import sys
import cv2
import numpy as np

def test_grounded_sam():
    """测试Grounded SAM环境和基本功能"""
    
    print("🔍 测试Grounded SAM环境...")
    
    # 检查基本依赖
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✅ GPU设备: {torch.cuda.get_device_name(0)}")
    except ImportError as e:
        print(f"❌ PyTorch导入失败: {e}")
        return False
    
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
    except ImportError as e:
        print(f"❌ OpenCV导入失败: {e}")
        return False
    
    # 检查图像文件
    image_path = "/home/<USER>/Infusion-main-cism/data/rednet/images/00000.png"
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    print(f"✅ 图像文件存在: {image_path}")
    
    # 读取图像信息
    try:
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return False
        
        height, width = image.shape[:2]
        print(f"✅ 图像尺寸: {width}x{height}")
        
    except Exception as e:
        print(f"❌ 读取图像失败: {e}")
        return False
    
    # 检查模型权重文件
    weights_dir = "/home/<USER>/SAM/grounded_sam_tools/weights"
    required_files = [
        "sam_vit_h_4b8939.pth",
        "groundingdino_swint_ogc.pth", 
        "GroundingDINO_SwinT_OGC.py"
    ]
    
    print("\n🔍 检查模型权重文件...")
    for file in required_files:
        file_path = os.path.join(weights_dir, file)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / (1024*1024)  # MB
            print(f"✅ {file}: {size:.1f}MB")
        else:
            print(f"❌ 缺失文件: {file}")
            return False
    
    print("\n🎯 环境检查完成！")
    print("\n📋 可以使用以下命令创建掩码:")
    print("\n方案A - SAM交互式 (需要图形界面):")
    print("python /home/<USER>/Infusion-main-cism/mask_creation_guide/sam_mask_creator.py \\")
    print("  --model_path /home/<USER>/SAM/sam_tools/weights/sam_vit_h_4b8939.pth \\")
    print("  --model_type vit_h \\")
    print(f"  --image_path {image_path} \\")
    print("  --output_dir /home/<USER>/Infusion-main-cism/data/rednet/seg")
    
    print("\n方案B - Grounded SAM文本式 (推荐):")
    print("python /home/<USER>/Infusion-main-cism/mask_creation_guide/grounded_sam_creator.py \\")
    print("  --sam_checkpoint /home/<USER>/SAM/grounded_sam_tools/weights/sam_vit_h_4b8939.pth \\")
    print("  --grounding_dino_config /home/<USER>/SAM/grounded_sam_tools/weights/GroundingDINO_SwinT_OGC.py \\")
    print("  --grounding_dino_checkpoint /home/<USER>/SAM/grounded_sam_tools/weights/groundingdino_swint_ogc.pth \\")
    print(f"  --image_path {image_path} \\")
    print("  --text_prompt 'person . car . object' \\")
    print("  --output_path /home/<USER>/Infusion-main-cism/data/rednet/seg/00000.png")
    
    print("\n💡 文本提示示例:")
    print("  - 'person . people . human'     # 移除人物")
    print("  - 'car . vehicle . automobile'  # 移除车辆") 
    print("  - 'chair . table . furniture'   # 移除家具")
    print("  - 'tree . plant . vegetation'   # 移除植物")
    print("  - 'building . house . wall'     # 移除建筑")
    
    return True

def create_simple_mask():
    """创建一个简单的测试掩码"""
    
    print("\n🎨 创建简单测试掩码...")
    
    image_path = "/home/<USER>/Infusion-main-cism/data/rednet/images/00000.png"
    output_dir = "/home/<USER>/Infusion-main-cism/data/rednet/seg"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 读取图像
        image = cv2.imread(image_path)
        height, width = image.shape[:2]
        
        # 创建一个简单的测试掩码（中心区域为白色）
        mask = np.zeros((height, width), dtype=np.uint8)
        
        # 在中心创建一个矩形掩码区域
        center_x, center_y = width // 2, height // 2
        mask_width, mask_height = width // 4, height // 4
        
        x1 = center_x - mask_width // 2
        y1 = center_y - mask_height // 2
        x2 = center_x + mask_width // 2
        y2 = center_y + mask_height // 2
        
        mask[y1:y2, x1:x2] = 255  # 白色区域表示需要修复的部分
        
        # 保存掩码
        output_path = os.path.join(output_dir, "00000_test_mask.png")
        cv2.imwrite(output_path, mask)
        
        print(f"✅ 测试掩码已保存: {output_path}")
        print(f"📏 掩码尺寸: {width}x{height}")
        print(f"🎯 掩码区域: 中心 {mask_width}x{mask_height} 像素")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建测试掩码失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Grounded SAM测试工具")
    print("======================")
    
    # 测试环境
    if test_grounded_sam():
        print("\n" + "="*50)
        
        # 创建测试掩码
        create_simple_mask()
        
        print("\n🎉 测试完成！")
        print("\n📋 下一步:")
        print("1. 如果需要精确掩码，使用上面显示的命令")
        print("2. 根据图像内容调整文本提示")
        print("3. 检查生成的掩码质量")
        
    else:
        print("\n❌ 环境测试失败，请检查安装")
        sys.exit(1)
