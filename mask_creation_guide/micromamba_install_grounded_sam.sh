#!/bin/bash

# Grounded SAM micromamba安装脚本
# 适配用户的micromamba环境

echo "🚀 开始为micromamba安装 Grounded SAM..."

# 检查conda环境管理器
CONDA_CMD=""

if command -v micromamba &> /dev/null; then
    CONDA_CMD="micromamba"
    echo "✅ 检测到micromamba"
elif command -v mamba &> /dev/null; then
    CONDA_CMD="mamba"
    echo "✅ 检测到mamba"
elif command -v conda &> /dev/null; then
    CONDA_CMD="conda"
    echo "✅ 检测到conda"
else
    echo "❌ 错误: 未找到conda环境管理器"
    echo "请确保已安装conda、mamba或micromamba"
    exit 1
fi

echo "🔧 使用环境管理器: ${CONDA_CMD}"

# 设置安装目录
SAM_BASE_DIR="/home/<USER>/SAM"
PROJECT_DIR="/home/<USER>/Infusion-main-cism"
GROUNDED_SAM_DIR="${SAM_BASE_DIR}/grounded_sam_tools/Grounded-Segment-Anything"
WEIGHTS_DIR="${SAM_BASE_DIR}/grounded_sam_tools/weights"

echo "📁 安装目录设置:"
echo "   SAM基础目录: ${SAM_BASE_DIR}"
echo "   项目目录: ${PROJECT_DIR}"
echo "   Grounded SAM目录: ${GROUNDED_SAM_DIR}"
echo "   模型权重目录: ${WEIGHTS_DIR}"

# 创建目录
mkdir -p "${GROUNDED_SAM_DIR}"
mkdir -p "${WEIGHTS_DIR}"

# 进入Grounded SAM目录
cd "${GROUNDED_SAM_DIR}"

# 克隆仓库
if [ ! -d ".git" ]; then
    echo "📥 克隆 Grounded-Segment-Anything 仓库..."
    git clone https://github.com/IDEA-Research/Grounded-Segment-Anything.git .
else
    echo "✅ Grounded SAM仓库已存在，更新代码..."
    git pull
fi

# 创建Grounded SAM专用环境
ENV_NAME="grounded_sam_tools"
echo "🔧 创建micromamba环境: ${ENV_NAME}"

# 检查环境是否已存在
if ${CONDA_CMD} env list | grep -q "${ENV_NAME}"; then
    echo "⚠️  环境 ${ENV_NAME} 已存在，是否重新创建? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "🗑️  删除现有环境..."
        ${CONDA_CMD} env remove -n "${ENV_NAME}" -y
    else
        echo "📦 使用现有环境 ${ENV_NAME}"
        if [[ "${CONDA_CMD}" == "micromamba" ]]; then
            eval "$(micromamba shell hook --shell bash)"
            micromamba activate "${ENV_NAME}"
        else
            conda activate "${ENV_NAME}"
        fi
        exit 0
    fi
fi

# 创建新环境
${CONDA_CMD} create -n "${ENV_NAME}" python=3.8 -y

# 激活环境
echo "🔄 激活环境 ${ENV_NAME}..."
if [[ "${CONDA_CMD}" == "micromamba" ]]; then
    eval "$(micromamba shell hook --shell bash)"
    micromamba activate "${ENV_NAME}"
else
    conda activate "${ENV_NAME}"
fi

# 验证环境激活
if [[ "$CONDA_DEFAULT_ENV" != "${ENV_NAME}" ]]; then
    echo "❌ 环境激活失败"
    exit 1
fi

echo "✅ 环境 ${ENV_NAME} 激活成功"

# 安装PyTorch
echo "🔧 安装PyTorch..."
if command -v nvidia-smi &> /dev/null; then
    CUDA_VERSION=$(nvidia-smi | grep "CUDA Version" | awk '{print $9}' | cut -d. -f1,2)
    echo "🎮 检测到CUDA版本: ${CUDA_VERSION}"
    
    if [[ "$CUDA_VERSION" == "11.8" ]] || [[ "$CUDA_VERSION" == "11.7" ]]; then
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    elif [[ "$CUDA_VERSION" == "12.1" ]] || [[ "$CUDA_VERSION" == "12.0" ]]; then
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
    else
        echo "⚠️  未识别的CUDA版本，安装CPU版本PyTorch"
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
    fi
else
    echo "💻 未检测到CUDA，安装CPU版本PyTorch"
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
fi

# 安装Grounded SAM依赖
echo "📦 安装Grounded SAM依赖..."
pip install -e .
pip install opencv-python
pip install pillow
pip install numpy
pip install matplotlib
pip install transformers
pip install accelerate
pip install supervision

# 下载模型权重
echo "📥 下载模型权重..."
cd "${WEIGHTS_DIR}"

# 下载SAM权重
echo "下载 SAM ViT-H 权重..."
if [ ! -f "sam_vit_h_4b8939.pth" ]; then
    wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
else
    echo "✅ sam_vit_h_4b8939.pth 已存在"
fi

# 下载GroundingDINO权重
echo "下载 GroundingDINO 权重..."
if [ ! -f "groundingdino_swint_ogc.pth" ]; then
    wget https://github.com/IDEA-Research/GroundingDINO/releases/download/v0.1.0-alpha/groundingdino_swint_ogc.pth
else
    echo "✅ groundingdino_swint_ogc.pth 已存在"
fi

# 下载GroundingDINO配置文件
echo "下载 GroundingDINO 配置文件..."
if [ ! -f "GroundingDINO_SwinT_OGC.py" ]; then
    wget https://raw.githubusercontent.com/IDEA-Research/GroundingDINO/main/groundingdino/config/GroundingDINO_SwinT_OGC.py
else
    echo "✅ GroundingDINO_SwinT_OGC.py 已存在"
fi

# 创建环境激活脚本
ACTIVATE_SCRIPT="${SAM_BASE_DIR}/activate_grounded_sam.sh"
cat > "${ACTIVATE_SCRIPT}" << EOF
#!/bin/bash
# Grounded SAM环境激活脚本

echo "🔄 激活Grounded SAM环境..."
if command -v micromamba &> /dev/null; then
    eval "\$(micromamba shell hook --shell bash)"
    micromamba activate ${ENV_NAME}
elif command -v mamba &> /dev/null; then
    conda activate ${ENV_NAME}
else
    conda activate ${ENV_NAME}
fi

echo "✅ Grounded SAM环境已激活: \${CONDA_DEFAULT_ENV}"
echo "📁 项目目录: ${PROJECT_DIR}"
echo "🛠️  Grounded SAM目录: ${GROUNDED_SAM_DIR}"
echo "🎯 模型权重目录: ${WEIGHTS_DIR}"

# 设置环境变量
export GROUNDED_SAM_WEIGHTS_DIR="${WEIGHTS_DIR}"
export GROUNDED_SAM_TOOLS_DIR="${GROUNDED_SAM_DIR}"

echo ""
echo "🚀 可用命令:"
echo "   python ${PROJECT_DIR}/mask_creation_guide/grounded_sam_creator.py --help"
EOF

chmod +x "${ACTIVATE_SCRIPT}"

# 创建快速使用脚本
QUICK_START="${SAM_BASE_DIR}/grounded_sam_quick_start.sh"
cat > "${QUICK_START}" << EOF
#!/bin/bash
# Grounded SAM快速启动脚本

# 激活环境
source ${ACTIVATE_SCRIPT}

# 检查参数
if [ \$# -eq 0 ]; then
    echo "使用方法:"
    echo "  \$0 single <image_path> <text_prompt> <output_path>  # 单张图像"
    echo "  \$0 batch <images_dir> <config_path> <output_dir>   # 批量处理"
    echo ""
    echo "示例:"
    echo "  \$0 single ./test.jpg 'car . person' ./mask.jpg"
    echo "  \$0 batch ./images ./text_config.json ./seg"
    exit 1
fi

MODE=\$1

if [ "\$MODE" = "single" ]; then
    if [ \$# -ne 4 ]; then
        echo "错误: 单张模式需要 <image_path> <text_prompt> <output_path>"
        exit 1
    fi
    
    IMAGE_PATH=\$2
    TEXT_PROMPT=\$3
    OUTPUT_PATH=\$4
    
    echo "🎯 处理单张图像..."
    python ${PROJECT_DIR}/mask_creation_guide/grounded_sam_creator.py \\
        --sam_checkpoint ${WEIGHTS_DIR}/sam_vit_h_4b8939.pth \\
        --grounding_dino_config ${WEIGHTS_DIR}/GroundingDINO_SwinT_OGC.py \\
        --grounding_dino_checkpoint ${WEIGHTS_DIR}/groundingdino_swint_ogc.pth \\
        --image_path "\$IMAGE_PATH" \\
        --text_prompt "\$TEXT_PROMPT" \\
        --output_path "\$OUTPUT_PATH"

elif [ "\$MODE" = "batch" ]; then
    if [ \$# -ne 4 ]; then
        echo "错误: 批量模式需要 <images_dir> <config_path> <output_dir>"
        exit 1
    fi
    
    IMAGES_DIR=\$2
    CONFIG_PATH=\$3
    OUTPUT_DIR=\$4
    
    echo "🔄 批量处理图像..."
    python ${PROJECT_DIR}/mask_creation_guide/grounded_sam_creator.py \\
        --sam_checkpoint ${WEIGHTS_DIR}/sam_vit_h_4b8939.pth \\
        --grounding_dino_config ${WEIGHTS_DIR}/GroundingDINO_SwinT_OGC.py \\
        --grounding_dino_checkpoint ${WEIGHTS_DIR}/groundingdino_swint_ogc.pth \\
        --images_dir "\$IMAGES_DIR" \\
        --config_path "\$CONFIG_PATH" \\
        --output_dir "\$OUTPUT_DIR"
else
    echo "错误: 未知模式 '\$MODE'"
    exit 1
fi
EOF

chmod +x "${QUICK_START}"

echo ""
echo "🎉 Grounded SAM安装完成！"
echo ""
echo "📋 安装总结:"
echo "   ✅ micromamba环境: ${ENV_NAME}"
echo "   ✅ Grounded SAM目录: ${GROUNDED_SAM_DIR}"
echo "   ✅ 模型权重目录: ${WEIGHTS_DIR}"
echo "   ✅ 激活脚本: ${ACTIVATE_SCRIPT}"
echo "   ✅ 快速启动: ${QUICK_START}"
echo ""
echo "🚀 使用方法:"
echo "   1. 激活环境: source ${ACTIVATE_SCRIPT}"
echo "   2. 单张处理: ${QUICK_START} single ./test.jpg 'car . person' ./mask.jpg"
echo "   3. 批量处理: ${QUICK_START} batch ./images ./config.json ./seg"
