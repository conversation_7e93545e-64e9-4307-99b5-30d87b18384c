#!/bin/bash

# SAM (Segment and Track Anything) 安装脚本

echo "开始安装 Segment and Track Anything..."

# 1. 克隆仓库
git clone https://github.com/z-x-yang/Segment-and-Track-Anything.git
cd Segment-and-Track-Anything

# 2. 创建conda环境
conda create -n sam_env python=3.8 -y
conda activate sam_env

# 3. 安装依赖
pip install torch torchvision torchaudio
pip install opencv-python pillow numpy matplotlib
pip install segment-anything
pip install gradio

# 4. 下载SAM模型权重
mkdir checkpoints
cd checkpoints

# 下载不同大小的SAM模型（选择一个即可）
# ViT-H (最大最准确，推荐)
wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

# ViT-L (中等大小)
# wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_l_0b3195.pth

# ViT-B (最小最快)
# wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth

cd ..

echo "SAM 安装完成！"
echo "激活环境: conda activate sam_env"
echo "运行: python app.py"
