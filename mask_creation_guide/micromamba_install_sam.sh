#!/bin/bash

# SAM (Segment and Track Anything) micromamba安装脚本
# 适配用户的micromamba环境

echo "🚀 开始为micromamba安装 Segment and Track Anything..."

# 检查conda环境管理器
CONDA_CMD=""

if command -v micromamba &> /dev/null; then
    CONDA_CMD="micromamba"
    echo "✅ 检测到micromamba"
elif command -v mamba &> /dev/null; then
    CONDA_CMD="mamba"
    echo "✅ 检测到mamba"
elif command -v conda &> /dev/null; then
    CONDA_CMD="conda"
    echo "✅ 检测到conda"
else
    echo "❌ 错误: 未找到conda环境管理器"
    echo "请确保已安装conda、mamba或micromamba"
    exit 1
fi

echo "🔧 使用环境管理器: ${CONDA_CMD}"

# 设置安装目录 - 在SAM专用目录下创建
SAM_BASE_DIR="/home/<USER>/SAM"
PROJECT_DIR="/home/<USER>/Infusion-main-cism"
SAM_DIR="${SAM_BASE_DIR}/sam_tools/Segment-and-Track-Anything"
WEIGHTS_DIR="${SAM_BASE_DIR}/sam_tools/weights"

echo "📁 安装目录设置:"
echo "   SAM基础目录: ${SAM_BASE_DIR}"
echo "   项目目录: ${PROJECT_DIR}"
echo "   SAM工具目录: ${SAM_DIR}"
echo "   模型权重目录: ${WEIGHTS_DIR}"

# 创建目录
mkdir -p "${SAM_DIR}"
mkdir -p "${WEIGHTS_DIR}"

# 进入SAM目录
cd "${SAM_DIR}"

# 克隆仓库（如果还没有的话）
if [ ! -d ".git" ]; then
    echo "📥 克隆 Segment-and-Track-Anything 仓库..."
    git clone https://github.com/z-x-yang/Segment-and-Track-Anything.git .
else
    echo "✅ SAM仓库已存在，更新代码..."
    git pull
fi

# 创建SAM专用环境
ENV_NAME="sam_tools"
echo "🔧 创建micromamba环境: ${ENV_NAME}"

# 检查环境是否已存在
if ${CONDA_CMD} env list | grep -q "${ENV_NAME}"; then
    echo "⚠️  环境 ${ENV_NAME} 已存在，是否重新创建? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo "🗑️  删除现有环境..."
        ${CONDA_CMD} env remove -n "${ENV_NAME}" -y
    else
        echo "📦 使用现有环境 ${ENV_NAME}"
        if [[ "${CONDA_CMD}" == "micromamba" ]]; then
            eval "$(micromamba shell hook --shell bash)"
            micromamba activate "${ENV_NAME}"
        else
            conda activate "${ENV_NAME}"
        fi
        exit 0
    fi
fi

# 创建新环境
${CONDA_CMD} create -n "${ENV_NAME}" python=3.8 -y

# 激活环境
echo "🔄 激活环境 ${ENV_NAME}..."
if [[ "${CONDA_CMD}" == "micromamba" ]]; then
    eval "$(micromamba shell hook --shell bash)"
    micromamba activate "${ENV_NAME}"
else
    conda activate "${ENV_NAME}"
fi

# 验证环境激活
if [[ "$CONDA_DEFAULT_ENV" != "${ENV_NAME}" ]]; then
    echo "❌ 环境激活失败"
    exit 1
fi

echo "✅ 环境 ${ENV_NAME} 激活成功"

# 安装PyTorch (根据CUDA版本调整)
echo "🔧 安装PyTorch..."
# 检查CUDA版本
if command -v nvidia-smi &> /dev/null; then
    CUDA_VERSION=$(nvidia-smi | grep "CUDA Version" | awk '{print $9}' | cut -d. -f1,2)
    echo "🎮 检测到CUDA版本: ${CUDA_VERSION}"
    
    if [[ "$CUDA_VERSION" == "11.8" ]] || [[ "$CUDA_VERSION" == "11.7" ]]; then
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
    elif [[ "$CUDA_VERSION" == "12.1" ]] || [[ "$CUDA_VERSION" == "12.0" ]]; then
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
    else
        echo "⚠️  未识别的CUDA版本，安装CPU版本PyTorch"
        pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
    fi
else
    echo "💻 未检测到CUDA，安装CPU版本PyTorch"
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
fi

# 安装SAM相关依赖
echo "📦 安装SAM依赖包..."
pip install segment-anything
pip install opencv-python
pip install pillow
pip install numpy
pip install matplotlib
pip install gradio
pip install jupyter

# 下载SAM模型权重
echo "📥 下载SAM模型权重..."
cd "${WEIGHTS_DIR}"

# 下载不同大小的SAM模型
echo "下载 SAM ViT-H 模型 (最大最准确，推荐)..."
if [ ! -f "sam_vit_h_4b8939.pth" ]; then
    wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
else
    echo "✅ sam_vit_h_4b8939.pth 已存在"
fi

echo "下载 SAM ViT-L 模型 (中等大小)..."
if [ ! -f "sam_vit_l_0b3195.pth" ]; then
    wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_l_0b3195.pth
else
    echo "✅ sam_vit_l_0b3195.pth 已存在"
fi

echo "下载 SAM ViT-B 模型 (最小最快)..."
if [ ! -f "sam_vit_b_01ec64.pth" ]; then
    wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth
else
    echo "✅ sam_vit_b_01ec64.pth 已存在"
fi

# 创建环境激活脚本
ACTIVATE_SCRIPT="${SAM_BASE_DIR}/activate_sam.sh"
cat > "${ACTIVATE_SCRIPT}" << EOF
#!/bin/bash
# SAM环境激活脚本

echo "🔄 激活SAM工具环境..."
if command -v micromamba &> /dev/null; then
    eval "\$(micromamba shell hook --shell bash)"
    micromamba activate ${ENV_NAME}
elif command -v mamba &> /dev/null; then
    conda activate ${ENV_NAME}
else
    conda activate ${ENV_NAME}
fi

echo "✅ SAM环境已激活: \${CONDA_DEFAULT_ENV}"
echo "📁 项目目录: ${PROJECT_DIR}"
echo "🛠️  SAM工具目录: ${SAM_DIR}"
echo "🎯 模型权重目录: ${WEIGHTS_DIR}"

# 设置环境变量
export SAM_WEIGHTS_DIR="${WEIGHTS_DIR}"
export SAM_TOOLS_DIR="${SAM_DIR}"

echo ""
echo "🚀 可用命令:"
echo "   python ${PROJECT_DIR}/mask_creation_guide/sam_mask_creator.py --help"
echo "   python ${PROJECT_DIR}/mask_creation_guide/sam_batch_processor.py --help"
EOF

chmod +x "${ACTIVATE_SCRIPT}"

# 创建快速使用脚本
QUICK_START="${SAM_BASE_DIR}/sam_quick_start.sh"
cat > "${QUICK_START}" << EOF
#!/bin/bash
# SAM快速启动脚本

# 激活环境
source ${ACTIVATE_SCRIPT}

# 检查参数
if [ \$# -eq 0 ]; then
    echo "使用方法:"
    echo "  \$0 interactive <image_path>     # 交互式创建掩码"
    echo "  \$0 batch <images_dir> <config>  # 批量处理"
    echo ""
    echo "示例:"
    echo "  \$0 interactive ./images/test.jpg"
    echo "  \$0 batch ./images ./points_config.json"
    exit 1
fi

MODE=\$1

if [ "\$MODE" = "interactive" ]; then
    if [ \$# -ne 2 ]; then
        echo "错误: 交互模式需要图像路径"
        exit 1
    fi
    
    IMAGE_PATH=\$2
    OUTPUT_DIR="./seg"
    
    echo "🎯 启动交互式掩码创建..."
    python ${PROJECT_DIR}/mask_creation_guide/sam_mask_creator.py \\
        --model_path ${WEIGHTS_DIR}/sam_vit_h_4b8939.pth \\
        --model_type vit_h \\
        --image_path "\$IMAGE_PATH" \\
        --output_dir "\$OUTPUT_DIR"

elif [ "\$MODE" = "batch" ]; then
    if [ \$# -ne 3 ]; then
        echo "错误: 批量模式需要图像目录和配置文件"
        exit 1
    fi
    
    IMAGES_DIR=\$2
    CONFIG_PATH=\$3
    OUTPUT_DIR="./seg"
    
    echo "🔄 启动批量掩码处理..."
    python ${PROJECT_DIR}/mask_creation_guide/sam_batch_processor.py \\
        --model_path ${WEIGHTS_DIR}/sam_vit_h_4b8939.pth \\
        --images_dir "\$IMAGES_DIR" \\
        --config_path "\$CONFIG_PATH" \\
        --output_dir "\$OUTPUT_DIR"
else
    echo "错误: 未知模式 '\$MODE'"
    exit 1
fi
EOF

chmod +x "${QUICK_START}"

echo ""
echo "🎉 SAM安装完成！"
echo ""
echo "📋 安装总结:"
echo "   ✅ micromamba环境: ${ENV_NAME}"
echo "   ✅ SAM工具目录: ${SAM_DIR}"
echo "   ✅ 模型权重目录: ${WEIGHTS_DIR}"
echo "   ✅ 激活脚本: ${ACTIVATE_SCRIPT}"
echo "   ✅ 快速启动: ${QUICK_START}"
echo ""
echo "🚀 使用方法:"
echo "   1. 激活环境: source ${ACTIVATE_SCRIPT}"
echo "   2. 交互式使用: ${QUICK_START} interactive ./images/test.jpg"
echo "   3. 批量处理: ${QUICK_START} batch ./images ./config.json"
echo ""
echo "💡 提示: 所有文件都安装在项目目录下，不会影响系统环境"
