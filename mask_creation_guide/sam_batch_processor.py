#!/usr/bin/env python3
"""
SAM 批量掩码处理工具
用于批量处理多张图像
"""

import cv2
import numpy as np
import os
import json
from pathlib import Path
from segment_anything import SamPredictor, sam_model_registry
import argparse

class SAMBatchProcessor:
    def __init__(self, model_path, model_type="vit_h"):
        """初始化SAM模型"""
        print(f"加载SAM模型: {model_type}")
        self.sam = sam_model_registry[model_type](checkpoint=model_path)
        self.predictor = SamPredictor(self.sam)
        
    def load_points_config(self, config_path):
        """
        加载点击点配置文件
        
        配置文件格式:
        {
            "image1.jpg": {
                "positive_points": [[x1, y1], [x2, y2]],
                "negative_points": [[x3, y3]]
            }
        }
        """
        with open(config_path, 'r') as f:
            return json.load(f)
    
    def process_single_image(self, image_path, points_config, output_dir):
        """处理单张图像"""
        # 加载图像
        image = cv2.imread(image_path)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        self.predictor.set_image(image_rgb)
        
        # 获取图像名称
        image_name = Path(image_path).name
        
        if image_name not in points_config:
            print(f"跳过 {image_name}: 配置中未找到点击点")
            return
        
        config = points_config[image_name]
        
        # 准备点击点
        input_points = []
        input_labels = []
        
        # 添加正点
        if "positive_points" in config:
            for point in config["positive_points"]:
                input_points.append(point)
                input_labels.append(1)
        
        # 添加负点
        if "negative_points" in config:
            for point in config["negative_points"]:
                input_points.append(point)
                input_labels.append(0)
        
        if len(input_points) == 0:
            print(f"跳过 {image_name}: 没有有效的点击点")
            return
        
        # 转换为numpy数组
        input_points = np.array(input_points)
        input_labels = np.array(input_labels)
        
        # 预测掩码
        masks, scores, logits = self.predictor.predict(
            point_coords=input_points,
            point_labels=input_labels,
            multimask_output=True,
        )
        
        # 选择最佳掩码
        best_mask = masks[np.argmax(scores)]
        
        # 保存掩码
        mask_image = (best_mask * 255).astype(np.uint8)
        output_path = os.path.join(output_dir, image_name)
        cv2.imwrite(output_path, mask_image)
        
        print(f"处理完成: {image_name}, 得分: {scores[np.argmax(scores)]:.3f}")
    
    def process_batch(self, images_dir, points_config_path, output_dir):
        """批量处理图像"""
        # 加载配置
        points_config = self.load_points_config(points_config_path)
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取所有图像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(Path(images_dir).glob(f"*{ext}"))
            image_files.extend(Path(images_dir).glob(f"*{ext.upper()}"))
        
        print(f"找到 {len(image_files)} 张图像")
        
        # 处理每张图像
        for image_path in image_files:
            try:
                self.process_single_image(str(image_path), points_config, output_dir)
            except Exception as e:
                print(f"处理 {image_path.name} 时出错: {e}")

def create_sample_config():
    """创建示例配置文件"""
    sample_config = {
        "example_image.jpg": {
            "positive_points": [[100, 150], [200, 250]],
            "negative_points": [[50, 50]]
        },
        "another_image.jpg": {
            "positive_points": [[300, 400]],
            "negative_points": []
        }
    }
    
    with open("points_config_sample.json", 'w') as f:
        json.dump(sample_config, f, indent=2)
    
    print("示例配置文件已创建: points_config_sample.json")

def main():
    parser = argparse.ArgumentParser(description="SAM批量掩码处理工具")
    parser.add_argument("--model_path", required=True, help="SAM模型权重路径")
    parser.add_argument("--model_type", default="vit_h", choices=["vit_h", "vit_l", "vit_b"], help="模型类型")
    parser.add_argument("--images_dir", help="输入图像目录")
    parser.add_argument("--config_path", help="点击点配置文件路径")
    parser.add_argument("--output_dir", help="输出目录")
    parser.add_argument("--create_sample", action="store_true", help="创建示例配置文件")
    
    args = parser.parse_args()
    
    if args.create_sample:
        create_sample_config()
        return
    
    if not all([args.images_dir, args.config_path, args.output_dir]):
        print("错误: 需要提供 --images_dir, --config_path, --output_dir 参数")
        return
    
    # 创建批量处理器
    processor = SAMBatchProcessor(args.model_path, args.model_type)
    
    # 批量处理
    processor.process_batch(args.images_dir, args.config_path, args.output_dir)

if __name__ == "__main__":
    main()
