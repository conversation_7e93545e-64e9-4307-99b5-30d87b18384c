#!/bin/bash

# 演示为 00000.png 创建掩码的两种方法

echo "🎯 为图像创建掩码演示"
echo "====================="
echo ""

IMAGE_PATH="/home/<USER>/Infusion-main-cism/data/rednet/images/00000.png"
OUTPUT_DIR="/home/<USER>/Infusion-main-cism/data/rednet/seg"

echo "📸 目标图像: $IMAGE_PATH"
echo "📁 输出目录: $OUTPUT_DIR"
echo ""

# 检查图像是否存在
if [ ! -f "$IMAGE_PATH" ]; then
    echo "❌ 图像文件不存在: $IMAGE_PATH"
    exit 1
fi

echo "✅ 图像文件存在"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"
echo "✅ 输出目录已创建: $OUTPUT_DIR"

echo ""
echo "🔧 方案A：SAM (点击式分割) - 高精度交互式"
echo "=========================================="
echo ""
echo "📋 使用步骤："
echo "1. 激活SAM环境:"
echo "   source /home/<USER>/SAM/activate_sam.sh"
echo ""
echo "2. 运行交互式掩码创建:"
echo "   python /home/<USER>/Infusion-main-cism/mask_creation_guide/sam_mask_creator.py \\"
echo "     --model_path /home/<USER>/SAM/sam_tools/weights/sam_vit_h_4b8939.pth \\"
echo "     --model_type vit_h \\"
echo "     --image_path $IMAGE_PATH \\"
echo "     --output_dir $OUTPUT_DIR"
echo ""
echo "📝 操作说明："
echo "   - 左键点击: 添加正点（要分割的区域）"
echo "   - 右键点击: 添加负点（不要分割的区域）"
echo "   - 回车键: 生成掩码"
echo "   - C键: 清除所有点"
echo "   - S键: 保存掩码"
echo ""

echo "🔧 方案B：Grounded SAM (文本分割) - 自动化"
echo "=========================================="
echo ""
echo "📋 使用步骤："
echo "1. 激活Grounded SAM环境:"
echo "   source /home/<USER>/SAM/activate_grounded_sam.sh"
echo ""
echo "2. 单张图像文本分割:"
echo "   python /home/<USER>/Infusion-main-cism/mask_creation_guide/grounded_sam_creator.py \\"
echo "     --sam_checkpoint /home/<USER>/SAM/grounded_sam_tools/weights/sam_vit_h_4b8939.pth \\"
echo "     --grounding_dino_config /home/<USER>/SAM/grounded_sam_tools/weights/GroundingDINO_SwinT_OGC.py \\"
echo "     --grounding_dino_checkpoint /home/<USER>/SAM/grounded_sam_tools/weights/groundingdino_swint_ogc.pth \\"
echo "     --image_path $IMAGE_PATH \\"
echo "     --text_prompt 'object . person . car . furniture' \\"
echo "     --output_path $OUTPUT_DIR/00000.png"
echo ""
echo "📝 文本提示示例："
echo "   - 'car . vehicle'           # 移除汽车"
echo "   - 'person . people . human' # 移除人物"
echo "   - 'chair . table . desk'    # 移除家具"
echo "   - 'tree . plant'            # 移除植物"
echo "   - 'building . house'        # 移除建筑"
echo ""

echo "💡 实际执行命令："
echo ""
echo "方案A - SAM交互式:"
echo "=================="
cat << 'EOF'
# 1. 激活环境
source /home/<USER>/SAM/activate_sam.sh

# 2. 运行交互式工具
python /home/<USER>/Infusion-main-cism/mask_creation_guide/sam_mask_creator.py \
  --model_path /home/<USER>/SAM/sam_tools/weights/sam_vit_h_4b8939.pth \
  --model_type vit_h \
  --image_path /home/<USER>/Infusion-main-cism/data/rednet/images/00000.png \
  --output_dir /home/<USER>/Infusion-main-cism/data/rednet/seg
EOF

echo ""
echo "方案B - Grounded SAM文本式:"
echo "=========================="
cat << 'EOF'
# 1. 激活环境
source /home/<USER>/SAM/activate_grounded_sam.sh

# 2. 运行文本分割（示例：移除人物和车辆）
python /home/<USER>/Infusion-main-cism/mask_creation_guide/grounded_sam_creator.py \
  --sam_checkpoint /home/<USER>/SAM/grounded_sam_tools/weights/sam_vit_h_4b8939.pth \
  --grounding_dino_config /home/<USER>/SAM/grounded_sam_tools/weights/GroundingDINO_SwinT_OGC.py \
  --grounding_dino_checkpoint /home/<USER>/SAM/grounded_sam_tools/weights/groundingdino_swint_ogc.pth \
  --image_path /home/<USER>/Infusion-main-cism/data/rednet/images/00000.png \
  --text_prompt "person . car . vehicle" \
  --output_path /home/<USER>/Infusion-main-cism/data/rednet/seg/00000.png
EOF

echo ""
echo "🎯 选择建议："
echo "============"
echo "✅ 如果您知道要移除什么物体，推荐使用 方案B (Grounded SAM)"
echo "✅ 如果需要精确控制分割边界，推荐使用 方案A (SAM交互式)"
echo "✅ 对于批量处理，推荐使用 方案B (Grounded SAM)"
echo ""
echo "🚀 现在您可以选择一种方法开始创建掩码！"
