#!/bin/bash

# 修复micromamba PATH配置脚本

echo "🔧 修复micromamba PATH配置"
echo "=========================="
echo ""

USER_HOME="/home/<USER>"
MICROMAMBA_DIR="$USER_HOME/micromamba"

echo "🔍 检查micromamba安装..."

# 检查可能的micromamba位置
POSSIBLE_MICROMAMBA_PATHS=(
    "$MICROMAMBA_DIR/bin/micromamba"
    "$MICROMAMBA_DIR/micromamba"
    "$MICROMAMBA_DIR/bin/conda"
    "$MICROMAMBA_DIR/bin/mamba"
)

FOUND_EXECUTABLE=""

for path in "${POSSIBLE_MICROMAMBA_PATHS[@]}"; do
    if [ -f "$path" ] && [ -x "$path" ]; then
        echo "✅ 找到可执行文件: $path"
        
        # 测试是否能运行
        if "$path" --version &>/dev/null; then
            version=$("$path" --version 2>/dev/null)
            echo "   版本: $version"
            FOUND_EXECUTABLE="$path"
            break
        else
            echo "   ⚠️  文件存在但无法运行"
        fi
    else
        echo "❌ 未找到: $path"
    fi
done

if [ -z "$FOUND_EXECUTABLE" ]; then
    echo ""
    echo "❌ 未找到可用的micromamba可执行文件"
    echo ""
    echo "🔍 让我们检查整个micromamba目录..."
    
    if [ -d "$MICROMAMBA_DIR" ]; then
        echo "📁 $MICROMAMBA_DIR 目录内容:"
        ls -la "$MICROMAMBA_DIR"
        echo ""
        
        echo "🔍 搜索所有可执行文件..."
        find "$MICROMAMBA_DIR" -type f -executable 2>/dev/null | head -10
        echo ""
        
        echo "🔍 搜索包含'mamba'的文件..."
        find "$MICROMAMBA_DIR" -name "*mamba*" 2>/dev/null
        echo ""
    else
        echo "❌ 目录不存在: $MICROMAMBA_DIR"
        echo ""
        echo "🔍 搜索整个用户目录..."
        find "$USER_HOME" -name "*micromamba*" -o -name "*conda*" 2>/dev/null | head -10
    fi
    
    echo ""
    echo "💡 建议："
    echo "1. 重新下载micromamba:"
    echo "   cd $USER_HOME"
    echo "   curl -Ls https://micro.mamba.pm/api/micromamba/linux-64/latest | tar -xvj bin/micromamba"
    echo "   mkdir -p micromamba/bin"
    echo "   mv bin/micromamba micromamba/bin/"
    echo ""
    echo "2. 或安装Miniconda:"
    echo "   wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
    echo "   bash Miniconda3-latest-Linux-x86_64.sh"
    
    exit 1
fi

echo ""
echo "✅ 找到可用的环境管理器: $FOUND_EXECUTABLE"
echo ""

# 确定bin目录
BIN_DIR=$(dirname "$FOUND_EXECUTABLE")
echo "📁 Bin目录: $BIN_DIR"

# 检查当前PATH
echo ""
echo "🔍 检查当前PATH配置..."

if echo "$PATH" | grep -q "$BIN_DIR"; then
    echo "✅ $BIN_DIR 已在PATH中"
else
    echo "❌ $BIN_DIR 不在PATH中"
    echo ""
    echo "🔧 修复PATH配置..."
    
    # 添加到.bashrc
    BASHRC="$USER_HOME/.bashrc"
    
    if [ -f "$BASHRC" ]; then
        echo "📝 备份现有.bashrc..."
        cp "$BASHRC" "$BASHRC.backup.$(date +%Y%m%d_%H%M%S)"
        
        echo "📝 添加PATH配置到.bashrc..."
        echo "" >> "$BASHRC"
        echo "# Micromamba PATH configuration - added by SAM installer" >> "$BASHRC"
        echo "export PATH=\"$BIN_DIR:\$PATH\"" >> "$BASHRC"
        
        echo "✅ PATH配置已添加到 $BASHRC"
    else
        echo "❌ .bashrc文件不存在，创建新的..."
        echo "export PATH=\"$BIN_DIR:\$PATH\"" > "$BASHRC"
        echo "✅ 创建了新的.bashrc文件"
    fi
fi

# 初始化shell
echo ""
echo "🔧 初始化shell配置..."

# 临时添加到当前session的PATH
export PATH="$BIN_DIR:$PATH"

# 获取可执行文件名
EXECUTABLE_NAME=$(basename "$FOUND_EXECUTABLE")

echo "🔄 运行 $EXECUTABLE_NAME init bash..."
if "$FOUND_EXECUTABLE" init bash &>/dev/null; then
    echo "✅ Shell初始化成功"
else
    echo "⚠️  Shell初始化可能失败，但继续..."
fi

# 测试环境管理器
echo ""
echo "🧪 测试环境管理器功能..."

echo "测试版本命令:"
if "$FOUND_EXECUTABLE" --version; then
    echo "✅ 版本命令正常"
else
    echo "❌ 版本命令失败"
fi

echo ""
echo "测试环境列表命令:"
if "$FOUND_EXECUTABLE" env list; then
    echo "✅ 环境列表命令正常"
else
    echo "❌ 环境列表命令失败"
fi

# 创建快速访问脚本
echo ""
echo "📝 创建快速访问脚本..."

QUICK_ACCESS="$USER_HOME/activate_micromamba.sh"
cat > "$QUICK_ACCESS" << EOF
#!/bin/bash
# 快速激活micromamba环境

# 添加到PATH
export PATH="$BIN_DIR:\$PATH"

# 初始化shell（如果需要）
if ! command -v $EXECUTABLE_NAME &> /dev/null; then
    echo "❌ $EXECUTABLE_NAME 仍然不可用"
    exit 1
fi

echo "✅ $EXECUTABLE_NAME 已可用"
echo "版本: \$($EXECUTABLE_NAME --version)"

# 如果提供了环境名，则激活
if [ \$# -eq 1 ]; then
    echo "🔄 激活环境: \$1"
    $FOUND_EXECUTABLE activate \$1
fi
EOF

chmod +x "$QUICK_ACCESS"
echo "✅ 创建快速访问脚本: $QUICK_ACCESS"

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 下一步操作："
echo "1. 重新加载shell配置:"
echo "   source ~/.bashrc"
echo ""
echo "2. 或者重新打开终端"
echo ""
echo "3. 验证修复:"
echo "   $EXECUTABLE_NAME --version"
echo ""
echo "4. 运行SAM安装脚本:"
echo "   ./mask_creation_guide/install_all.sh"
echo ""
echo "💡 如果仍有问题，可以使用快速访问脚本:"
echo "   source $QUICK_ACCESS"
