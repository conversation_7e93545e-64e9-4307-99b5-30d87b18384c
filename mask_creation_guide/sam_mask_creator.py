#!/usr/bin/env python3
"""
SAM 掩码创建工具
用于为 InFusion 项目创建高质量掩码
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from segment_anything import SamPredictor, sam_model_registry
import os
import argparse
from pathlib import Path

class SAMMaskCreator:
    def __init__(self, model_path, model_type="vit_h"):
        """
        初始化SAM模型
        
        Args:
            model_path: SAM模型权重路径
            model_type: 模型类型 ("vit_h", "vit_l", "vit_b")
        """
        print(f"加载SAM模型: {model_type}")
        self.sam = sam_model_registry[model_type](checkpoint=model_path)
        self.predictor = SamPredictor(self.sam)
        
    def load_image(self, image_path):
        """加载图像"""
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        return image
    
    def create_mask_interactive(self, image_path, output_dir):
        """
        交互式创建掩码
        
        Args:
            image_path: 输入图像路径
            output_dir: 输出目录
        """
        # 加载图像
        image = self.load_image(image_path)
        self.predictor.set_image(image)
        
        # 创建交互式界面
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 7))
        
        # 显示原图
        ax1.imshow(image)
        ax1.set_title("点击要分割的物体 (左键添加点，右键移除点，中键完成)")
        ax1.axis('off')
        
        # 初始化
        self.input_points = []
        self.input_labels = []
        self.current_mask = None
        
        def onclick(event):
            if event.inaxes != ax1:
                return
                
            x, y = int(event.xdata), int(event.ydata)
            
            if event.button == 1:  # 左键 - 添加正点
                self.input_points.append([x, y])
                self.input_labels.append(1)
                ax1.plot(x, y, 'go', markersize=8)
                print(f"添加正点: ({x}, {y})")
                
            elif event.button == 3:  # 右键 - 添加负点
                self.input_points.append([x, y])
                self.input_labels.append(0)
                ax1.plot(x, y, 'ro', markersize=8)
                print(f"添加负点: ({x}, {y})")
                
            elif event.button == 2:  # 中键 - 生成掩码
                if len(self.input_points) > 0:
                    self.generate_mask(ax2)
                    
            plt.draw()
        
        # 绑定点击事件
        fig.canvas.mpl_connect('button_press_event', onclick)
        
        # 添加键盘事件
        def onkey(event):
            if event.key == 'enter':  # 回车键生成掩码
                if len(self.input_points) > 0:
                    self.generate_mask(ax2)
            elif event.key == 'c':  # C键清除所有点
                self.clear_points(ax1)
            elif event.key == 's':  # S键保存掩码
                self.save_mask(image_path, output_dir)
                
        fig.canvas.mpl_connect('key_press_event', onkey)
        
        plt.tight_layout()
        plt.show()
    
    def generate_mask(self, ax):
        """生成掩码"""
        if len(self.input_points) == 0:
            return
            
        input_points = np.array(self.input_points)
        input_labels = np.array(self.input_labels)
        
        # 预测掩码
        masks, scores, logits = self.predictor.predict(
            point_coords=input_points,
            point_labels=input_labels,
            multimask_output=True,
        )
        
        # 选择最佳掩码
        best_mask = masks[np.argmax(scores)]
        self.current_mask = best_mask
        
        # 显示掩码
        ax.clear()
        ax.imshow(best_mask, cmap='gray')
        ax.set_title(f"生成的掩码 (得分: {scores[np.argmax(scores)]:.3f})")
        ax.axis('off')
        plt.draw()
        
        print(f"掩码生成完成，得分: {scores[np.argmax(scores)]:.3f}")
    
    def clear_points(self, ax):
        """清除所有点"""
        self.input_points = []
        self.input_labels = []
        ax.clear()
        # 重新显示原图
        image = self.load_image(self.current_image_path)
        ax.imshow(image)
        ax.set_title("点击要分割的物体")
        ax.axis('off')
        plt.draw()
        print("已清除所有点")
    
    def save_mask(self, image_path, output_dir):
        """保存掩码"""
        if self.current_mask is None:
            print("没有可保存的掩码")
            return
            
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取文件名
        image_name = Path(image_path).name
        mask_path = os.path.join(output_dir, image_name)
        
        # 转换掩码格式 (True -> 255白色, False -> 0黑色)
        mask_image = (self.current_mask * 255).astype(np.uint8)
        
        # 保存掩码
        cv2.imwrite(mask_path, mask_image)
        print(f"掩码已保存到: {mask_path}")

def main():
    parser = argparse.ArgumentParser(description="SAM掩码创建工具")
    parser.add_argument("--model_path", required=True, help="SAM模型权重路径")
    parser.add_argument("--model_type", default="vit_h", choices=["vit_h", "vit_l", "vit_b"], help="模型类型")
    parser.add_argument("--image_path", required=True, help="输入图像路径")
    parser.add_argument("--output_dir", required=True, help="输出目录")
    
    args = parser.parse_args()
    
    # 创建掩码生成器
    mask_creator = SAMMaskCreator(args.model_path, args.model_type)
    
    # 交互式创建掩码
    mask_creator.create_mask_interactive(args.image_path, args.output_dir)

if __name__ == "__main__":
    main()
