#!/bin/bash

# 修复环境冲突脚本

echo "🔧 修复SAM环境冲突问题"
echo "====================="
echo ""

# 清理所有环境变量
echo "🧹 清理环境变量..."
unset CONDA_DEFAULT_ENV
unset CONDA_PREFIX
unset CONDA_PYTHON_EXE
unset CONDA_EXE
unset PYTHONPATH

# 重新设置PATH
export PATH="/home/<USER>/bin:/usr/local/bin:/usr/bin:/bin"

echo "✅ 环境变量已清理"

# 重新激活micromamba
echo "🔄 重新初始化micromamba..."
eval "$(/home/<USER>/bin/micromamba shell hook --shell bash)"

# 激活sam_tools环境
echo "🔄 激活sam_tools环境..."
micromamba activate sam_tools

# 验证环境
echo "🔍 验证环境状态..."
echo "当前环境: $CONDA_DEFAULT_ENV"
echo "Python路径: $(which python)"
echo "Python版本: $(python --version)"

# 测试PyTorch导入
echo ""
echo "🧪 测试PyTorch导入..."
python -c "
import sys
print('Python executable:', sys.executable)
print('Python path:', sys.path[:3])

try:
    import torch
    print('✅ PyTorch导入成功')
    print('PyTorch版本:', torch.__version__)
    print('CUDA可用:', torch.cuda.is_available())
except Exception as e:
    print('❌ PyTorch导入失败:', e)
    exit(1)
"

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 环境修复成功！"
    echo ""
    echo "🚀 现在可以运行SAM工具:"
    echo "python /home/<USER>/Infusion-main-cism/mask_creation_guide/sam_mask_creator.py \\"
    echo "  --model_path /home/<USER>/SAM/sam_tools/weights/sam_vit_h_4b8939.pth \\"
    echo "  --model_type vit_h \\"
    echo "  --image_path /home/<USER>/Infusion-main-cism/data/rednet/images/00000.png \\"
    echo "  --output_dir /home/<USER>/Infusion-main-cism/data/rednet/seg"
else
    echo ""
    echo "❌ 环境修复失败"
    exit 1
fi
