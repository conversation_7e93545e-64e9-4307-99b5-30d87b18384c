#!/bin/bash

# 最终修复micromamba PATH配置脚本

echo "🔧 最终修复micromamba PATH配置"
echo "=============================="
echo ""

# 检查真正的micromamba位置
MICROMAMBA_PATH="/home/<USER>/bin/micromamba"

if [ -f "$MICROMAMBA_PATH" ] && [ -x "$MICROMAMBA_PATH" ]; then
    echo "✅ 找到micromamba: $MICROMAMBA_PATH"
    
    # 测试micromamba
    echo "🧪 测试micromamba功能..."
    if "$MICROMAMBA_PATH" --version &>/dev/null; then
        version=$("$MICROMAMBA_PATH" --version 2>/dev/null)
        echo "✅ micromamba版本: $version"
    else
        echo "❌ micromamba无法运行"
        exit 1
    fi
    
    # 检查PATH
    BIN_DIR=$(dirname "$MICROMAMBA_PATH")
    echo "📁 Bin目录: $BIN_DIR"
    
    if echo "$PATH" | grep -q "$BIN_DIR"; then
        echo "✅ $BIN_DIR 已在PATH中"
    else
        echo "❌ $BIN_DIR 不在PATH中，正在修复..."
        
        # 临时添加到当前session
        export PATH="$BIN_DIR:$PATH"
        echo "✅ 已临时添加到当前session的PATH"
        
        # 添加到.bashrc
        BASHRC="/home/<USER>/.bashrc"
        if [ -f "$BASHRC" ]; then
            # 检查是否已经存在
            if ! grep -q "$BIN_DIR" "$BASHRC"; then
                echo "📝 添加到.bashrc..."
                echo "" >> "$BASHRC"
                echo "# micromamba PATH - added by SAM installer" >> "$BASHRC"
                echo "export PATH=\"$BIN_DIR:\$PATH\"" >> "$BASHRC"
                echo "✅ 已添加到.bashrc"
            else
                echo "✅ .bashrc中已存在PATH配置"
            fi
        fi
    fi
    
    # 测试环境管理功能
    echo ""
    echo "🧪 测试环境管理功能..."
    
    echo "测试版本命令:"
    "$MICROMAMBA_PATH" --version
    
    echo ""
    echo "测试环境列表:"
    "$MICROMAMBA_PATH" env list
    
    echo ""
    echo "🎉 micromamba修复完成！"
    echo ""
    echo "📋 下一步:"
    echo "1. 重新加载shell配置:"
    echo "   source ~/.bashrc"
    echo ""
    echo "2. 验证修复:"
    echo "   micromamba --version"
    echo ""
    echo "3. 运行SAM安装脚本:"
    echo "   ./mask_creation_guide/install_all.sh"
    
else
    echo "❌ 未找到micromamba: $MICROMAMBA_PATH"
    exit 1
fi
