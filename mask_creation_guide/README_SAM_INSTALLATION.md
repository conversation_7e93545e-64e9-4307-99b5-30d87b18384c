# 🎯 SAM掩码工具安装指南 - 安装到 /home/<USER>/SAM

## 📋 修改总结

所有安装脚本已修改为安装到 `/home/<USER>/SAM` 目录，具体修改如下：

### 🏗️ 最终目录结构

```
/home/<USER>/SAM/
├── sam_tools/                          # SAM工具目录
│   ├── Segment-and-Track-Anything/     # SAM源码
│   └── weights/                        # SAM模型权重
│       ├── sam_vit_h_4b8939.pth       # 大模型(推荐)
│       ├── sam_vit_l_0b3195.pth       # 中模型
│       └── sam_vit_b_01ec64.pth       # 小模型
├── grounded_sam_tools/                 # Grounded SAM工具目录
│   ├── Grounded-Segment-Anything/     # Grounded SAM源码
│   └── weights/                       # Grounded SAM模型权重
│       ├── sam_vit_h_4b8939.pth       # SAM权重
│       ├── groundingdino_swint_ogc.pth # GroundingDINO权重
│       └── GroundingDINO_SwinT_OGC.py # 配置文件
├── activate_sam.sh                    # SAM环境激活脚本
├── activate_grounded_sam.sh           # Grounded SAM环境激活脚本
├── sam_quick_start.sh                 # SAM快速启动脚本
├── grounded_sam_quick_start.sh        # Grounded SAM快速启动脚本
└── mask_tools_usage.sh                # 统一使用指南
```

## 🚀 一键安装（推荐）

```bash
cd /home/<USER>/Infusion-main-cism
chmod +x mask_creation_guide/install_all.sh
./mask_creation_guide/install_all.sh
```

## 🎯 快速使用

### 安装完成后的使用方法

```bash
# 查看使用指南
/home/<USER>/SAM/mask_tools_usage.sh

# 测试环境
/home/<USER>/SAM/mask_tools_usage.sh test

# 使用SAM (点击式分割)
source /home/<USER>/SAM/activate_sam.sh
/home/<USER>/SAM/sam_quick_start.sh interactive /path/to/image.jpg

# 使用Grounded SAM (文本分割)
source /home/<USER>/SAM/activate_grounded_sam.sh
/home/<USER>/SAM/grounded_sam_quick_start.sh single /path/to/image.jpg "car . person" /path/to/mask.jpg
```

## 📝 修改的文件列表

1. **mask_creation_guide/install_all.sh** - 一键安装脚本
2. **mask_creation_guide/micromamba_install_sam.sh** - SAM安装脚本
3. **mask_creation_guide/micromamba_install_grounded_sam.sh** - Grounded SAM安装脚本
4. **mask_creation_guide/INSTALLATION_GUIDE.md** - 详细安装指南
5. **mask_creation_guide/README_SAM_INSTALLATION.md** - 本文件

## 🔧 环境管理

### micromamba环境
- `sam_tools` - SAM专用环境
- `grounded_sam_tools` - Grounded SAM专用环境

### 查看环境
```bash
micromamba env list
```

### 手动激活环境
```bash
# 激活SAM环境
micromamba activate sam_tools

# 激活Grounded SAM环境
micromamba activate grounded_sam_tools
```

## 🎯 针对InFusion项目的使用

### 为InFusion创建掩码的完整流程

1. **准备图像数据**
   ```bash
   your_scene/
   ├── images/
   │   ├── DSC07956.JPG
   │   ├── DSC07957.JPG
   │   └── ...
   └── seg/          # 这里将存放生成的掩码
   ```

2. **选择合适的工具**
   - **精度优先**：使用SAM (点击式)
   - **效率优先**：使用Grounded SAM (文本式)

3. **生成掩码**
   ```bash
   # 使用SAM
   source /home/<USER>/SAM/activate_sam.sh
   /home/<USER>/SAM/sam_quick_start.sh batch ./your_scene/images ./points_config.json

   # 或使用Grounded SAM
   source /home/<USER>/SAM/activate_grounded_sam.sh
   /home/<USER>/SAM/grounded_sam_quick_start.sh batch ./your_scene/images ./text_config.json ./your_scene/seg
   ```

## 🚨 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 使用较小的模型
   # 在脚本中将 sam_vit_h_4b8939.pth 改为 sam_vit_b_01ec64.pth
   ```

2. **环境激活失败**
   ```bash
   # 重新初始化micromamba
   micromamba shell init --shell bash
   source ~/.bashrc
   ```

3. **权重下载失败**
   ```bash
   # 手动下载权重文件到对应目录
   cd /home/<USER>/SAM/sam_tools/weights
   wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
   ```

## 🎉 安装验证

安装完成后，您可以通过以下命令验证安装：

```bash
# 检查SAM环境
source /home/<USER>/SAM/activate_sam.sh
python -c "import torch; print('PyTorch:', torch.__version__); print('CUDA available:', torch.cuda.is_available())"

# 检查Grounded SAM环境
source /home/<USER>/SAM/activate_grounded_sam.sh
python -c "import torch; print('PyTorch:', torch.__version__); print('CUDA available:', torch.cuda.is_available())"
```

## 💡 便捷别名（可选）

您可以将以下别名添加到 `~/.bashrc` 中：

```bash
# 添加到 ~/.bashrc
alias sam-tools='/home/<USER>/SAM/mask_tools_usage.sh'
alias sam-activate='source /home/<USER>/SAM/activate_sam.sh'
alias grounded-activate='source /home/<USER>/SAM/activate_grounded_sam.sh'
```

然后重新加载：
```bash
source ~/.bashrc
```

使用别名：
```bash
sam-tools          # 查看使用指南
sam-tools test     # 测试环境
sam-activate       # 激活SAM环境
grounded-activate  # 激活Grounded SAM环境
```

## 📖 详细文档

更详细的使用说明请查看：
- `mask_creation_guide/INSTALLATION_GUIDE.md` - 完整安装和使用指南
- `/home/<USER>/SAM/mask_tools_usage.sh` - 安装后的使用指南

现在您可以开始使用这些工具为InFusion项目创建高质量的掩码了！
