#!/bin/bash

# 环境诊断脚本
# 用于检查conda/mamba/micromamba环境

echo "🔍 环境诊断工具"
echo "================"
echo ""

echo "📋 检查conda环境管理器..."
echo ""

# 检查各种conda管理器
FOUND_MANAGERS=()

if command -v micromamba &> /dev/null; then
    echo "✅ micromamba: $(which micromamba)"
    echo "   版本: $(micromamba --version 2>/dev/null || echo '未知')"
    FOUND_MANAGERS+=("micromamba")
else
    echo "❌ micromamba: 未找到"
fi

if command -v mamba &> /dev/null; then
    echo "✅ mamba: $(which mamba)"
    echo "   版本: $(mamba --version 2>/dev/null || echo '未知')"
    FOUND_MANAGERS+=("mamba")
else
    echo "❌ mamba: 未找到"
fi

if command -v conda &> /dev/null; then
    echo "✅ conda: $(which conda)"
    echo "   版本: $(conda --version 2>/dev/null || echo '未知')"
    FOUND_MANAGERS+=("conda")
else
    echo "❌ conda: 未找到"
fi

echo ""

if [ ${#FOUND_MANAGERS[@]} -eq 0 ]; then
    echo "🚨 未找到任何conda环境管理器！"
    echo ""
    echo "📥 安装建议："
    echo "1. 安装Miniconda:"
    echo "   wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
    echo "   bash Miniconda3-latest-Linux-x86_64.sh"
    echo ""
    echo "2. 或安装micromamba:"
    echo "   curl -Ls https://micro.mamba.pm/api/micromamba/linux-64/latest | tar -xvj bin/micromamba"
    echo "   sudo mv bin/micromamba /usr/local/bin/"
    echo ""
    exit 1
else
    echo "✅ 找到 ${#FOUND_MANAGERS[@]} 个环境管理器: ${FOUND_MANAGERS[*]}"
fi

echo ""
echo "🔍 检查当前环境..."

# 检查当前激活的环境
if [ -n "$CONDA_DEFAULT_ENV" ]; then
    echo "✅ 当前激活环境: $CONDA_DEFAULT_ENV"
else
    echo "⚠️  当前未激活任何conda环境"
fi

# 检查conda前缀
if [ -n "$CONDA_PREFIX" ]; then
    echo "✅ Conda前缀: $CONDA_PREFIX"
else
    echo "⚠️  未设置CONDA_PREFIX"
fi

echo ""
echo "📋 列出所有环境..."

# 尝试列出环境
for manager in "${FOUND_MANAGERS[@]}"; do
    echo ""
    echo "🔧 使用 $manager 列出环境:"
    if $manager env list 2>/dev/null; then
        echo "✅ $manager 环境列表获取成功"
    else
        echo "❌ $manager 环境列表获取失败"
    fi
done

echo ""
echo "🔍 检查Python环境..."

# 检查Python
if command -v python &> /dev/null; then
    echo "✅ Python: $(which python)"
    echo "   版本: $(python --version)"
    echo "   路径: $(python -c 'import sys; print(sys.executable)')"
else
    echo "❌ Python: 未找到"
fi

if command -v python3 &> /dev/null; then
    echo "✅ Python3: $(which python3)"
    echo "   版本: $(python3 --version)"
else
    echo "❌ Python3: 未找到"
fi

echo ""
echo "🔍 检查重要环境变量..."

echo "PATH: $PATH"
echo "CONDA_DEFAULT_ENV: ${CONDA_DEFAULT_ENV:-'未设置'}"
echo "CONDA_PREFIX: ${CONDA_PREFIX:-'未设置'}"
echo "CONDA_EXE: ${CONDA_EXE:-'未设置'}"

echo ""
echo "🔍 检查shell配置..."

# 检查shell配置文件
SHELL_CONFIGS=(~/.bashrc ~/.bash_profile ~/.zshrc ~/.profile)

for config in "${SHELL_CONFIGS[@]}"; do
    if [ -f "$config" ]; then
        echo "📄 检查 $config:"
        if grep -q "conda\|mamba" "$config" 2>/dev/null; then
            echo "   ✅ 包含conda/mamba配置"
            grep -n "conda\|mamba" "$config" | head -3
        else
            echo "   ⚠️  未找到conda/mamba配置"
        fi
    fi
done

echo ""
echo "💡 建议的解决方案："
echo ""

if [ ${#FOUND_MANAGERS[@]} -gt 0 ]; then
    echo "✅ 您已有conda环境管理器，可以继续安装SAM工具"
    echo ""
    echo "🚀 推荐使用的管理器: ${FOUND_MANAGERS[0]}"
    echo ""
    echo "📝 如果安装脚本仍然失败，请尝试："
    echo "1. 重新初始化shell:"
    echo "   ${FOUND_MANAGERS[0]} shell init --shell bash"
    echo "   source ~/.bashrc"
    echo ""
    echo "2. 手动激活base环境:"
    echo "   ${FOUND_MANAGERS[0]} activate base"
    echo ""
    echo "3. 然后重新运行安装脚本"
else
    echo "❌ 需要先安装conda环境管理器"
fi

echo ""
echo "🔧 测试环境管理器功能..."

for manager in "${FOUND_MANAGERS[@]}"; do
    echo ""
    echo "测试 $manager:"
    
    # 测试基本命令
    if $manager --version &>/dev/null; then
        echo "  ✅ 版本命令正常"
    else
        echo "  ❌ 版本命令失败"
    fi
    
    if $manager env list &>/dev/null; then
        echo "  ✅ 环境列表命令正常"
    else
        echo "  ❌ 环境列表命令失败"
    fi
done

echo ""
echo "🎯 诊断完成！"
echo ""
echo "如果所有检查都通过，您可以重新运行SAM安装脚本："
echo "  ./mask_creation_guide/install_all.sh"
