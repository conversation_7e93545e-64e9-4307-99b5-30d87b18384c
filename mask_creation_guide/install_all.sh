#!/bin/bash

# 一键安装SAM和Grounded SAM工具
# 安装位置: /home/<USER>

echo "🚀 开始一键安装SAM掩码创建工具..."
echo "📁 安装位置: /home/<USER>/SAM"
echo ""

# 检查conda环境管理器
CONDA_CMD=""

if command -v micromamba &> /dev/null; then
    CONDA_CMD="micromamba"
    echo "✅ 检测到micromamba"
elif command -v mamba &> /dev/null; then
    CONDA_CMD="mamba"
    echo "✅ 检测到mamba"
elif command -v conda &> /dev/null; then
    CONDA_CMD="conda"
    echo "✅ 检测到conda"
else
    echo "❌ 错误: 未找到conda环境管理器"
    echo "请确保已安装以下任一工具："
    echo "  - micromamba: curl -Ls https://micro.mamba.pm/api/micromamba/linux-64/latest | tar -xvj bin/micromamba"
    echo "  - mamba: conda install mamba -n base -c conda-forge"
    echo "  - conda: 请安装Anaconda或Miniconda"
    exit 1
fi

echo "🔧 使用环境管理器: ${CONDA_CMD}"

# 设置目录
SAM_BASE_DIR="/home/<USER>/SAM"
PROJECT_DIR="/home/<USER>/Infusion-main-cism"

echo "📋 安装计划:"
echo "   SAM基础目录: ${SAM_BASE_DIR}"
echo "   项目目录: ${PROJECT_DIR}"
echo "   SAM工具: ${SAM_BASE_DIR}/sam_tools"
echo "   Grounded SAM工具: ${SAM_BASE_DIR}/grounded_sam_tools"
echo ""

# 询问用户是否继续
read -p "是否继续安装? (y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "安装已取消"
    exit 1
fi

echo ""
echo "🔧 开始安装..."

# 进入项目目录
cd "${PROJECT_DIR}"

# 安装SAM
echo ""
echo "📦 1/2 安装SAM工具..."
echo "================================"
if [ -f "mask_creation_guide/micromamba_install_sam.sh" ]; then
    chmod +x mask_creation_guide/micromamba_install_sam.sh
    ./mask_creation_guide/micromamba_install_sam.sh
    if [ $? -eq 0 ]; then
        echo "✅ SAM安装成功"
    else
        echo "❌ SAM安装失败"
        exit 1
    fi
else
    echo "❌ 未找到SAM安装脚本"
    exit 1
fi

echo ""
echo "📦 2/2 安装Grounded SAM工具..."
echo "================================"
if [ -f "mask_creation_guide/micromamba_install_grounded_sam.sh" ]; then
    chmod +x mask_creation_guide/micromamba_install_grounded_sam.sh
    ./mask_creation_guide/micromamba_install_grounded_sam.sh
    if [ $? -eq 0 ]; then
        echo "✅ Grounded SAM安装成功"
    else
        echo "❌ Grounded SAM安装失败"
        exit 1
    fi
else
    echo "❌ 未找到Grounded SAM安装脚本"
    exit 1
fi

# 创建统一的使用指南
USAGE_GUIDE="${SAM_BASE_DIR}/mask_tools_usage.sh"
cat > "${USAGE_GUIDE}" << 'EOF'
#!/bin/bash

# SAM掩码工具使用指南

echo "🎯 SAM掩码创建工具使用指南"
echo "=========================="
echo ""

echo "📋 可用工具:"
echo "   1. SAM (点击式分割) - 高精度，需要手动点击"
echo "   2. Grounded SAM (文本分割) - 自动化，使用文本描述"
echo ""

echo "🚀 快速使用:"
echo ""
echo "【SAM - 交互式分割】"
echo "   source /home/<USER>/SAM/activate_sam.sh"
echo "   /home/<USER>/SAM/sam_quick_start.sh interactive /path/to/image.jpg"
echo ""
echo "【Grounded SAM - 文本分割】"
echo "   source /home/<USER>/SAM/activate_grounded_sam.sh"
echo "   /home/<USER>/SAM/grounded_sam_quick_start.sh single /path/to/image.jpg 'car . person' /path/to/mask.jpg"
echo ""

echo "📖 详细文档:"
echo "   cat /home/<USER>/Infusion-main-cism/mask_creation_guide/INSTALLATION_GUIDE.md"
echo ""

echo "🔧 环境管理:"
echo "   micromamba env list                    # 查看所有环境"
echo "   micromamba activate sam_tools          # 激活SAM环境"
echo "   micromamba activate grounded_sam_tools # 激活Grounded SAM环境"
echo ""

if [ $# -eq 0 ]; then
    echo "💡 提示: 运行 '$0 help' 查看详细帮助"
    exit 0
fi

case "$1" in
    "help"|"--help"|"-h")
        echo "详细使用方法请查看:"
        echo "   /home/<USER>/Infusion-main-cism/mask_creation_guide/INSTALLATION_GUIDE.md"
        ;;
    "sam")
        echo "🔄 激活SAM环境..."
        source /home/<USER>/SAM/activate_sam.sh
        ;;
    "grounded")
        echo "🔄 激活Grounded SAM环境..."
        source /home/<USER>/SAM/activate_grounded_sam.sh
        ;;
    "test")
        echo "🧪 测试环境..."
        echo "测试SAM环境:"
        source /home/<USER>/SAM/activate_sam.sh
        python -c "import torch; print('✅ SAM环境正常, PyTorch:', torch.__version__)"

        echo "测试Grounded SAM环境:"
        source /home/<USER>/SAM/activate_grounded_sam.sh
        python -c "import torch; print('✅ Grounded SAM环境正常, PyTorch:', torch.__version__)"
        ;;
    *)
        echo "❌ 未知参数: $1"
        echo "可用参数: help, sam, grounded, test"
        ;;
esac
EOF

chmod +x "${USAGE_GUIDE}"

echo ""
echo "🎉 安装完成！"
echo "=============="
echo ""
echo "📁 安装位置:"
echo "   SAM工具: ${SAM_BASE_DIR}/sam_tools"
echo "   Grounded SAM工具: ${SAM_BASE_DIR}/grounded_sam_tools"
echo ""
echo "🚀 快速开始:"
echo "   ${USAGE_GUIDE}                    # 查看使用指南"
echo "   ${USAGE_GUIDE} test               # 测试环境"
echo "   ${USAGE_GUIDE} sam                # 激活SAM环境"
echo "   ${USAGE_GUIDE} grounded           # 激活Grounded SAM环境"
echo ""
echo "📖 详细文档:"
echo "   cat ${PROJECT_DIR}/mask_creation_guide/INSTALLATION_GUIDE.md"
echo ""
echo "💡 提示: 建议将以下命令添加到 ~/.bashrc 中:"
echo "   alias mask-tools='${USAGE_GUIDE}'"
echo ""

# 询问是否添加别名
read -p "是否自动添加别名到 ~/.bashrc? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "alias mask-tools='${USAGE_GUIDE}'" >> ~/.bashrc
    echo "✅ 别名已添加，重新加载终端后可使用 'mask-tools' 命令"
fi

echo ""
echo "🎯 现在您可以开始创建高质量掩码了！"
