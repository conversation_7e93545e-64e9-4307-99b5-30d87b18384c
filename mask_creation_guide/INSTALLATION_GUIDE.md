# 🎯 SAM掩码创建工具安装指南

## 📋 概述

本指南将帮您在 `/home/<USER>/SAM` 目录下安装两个强大的掩码创建工具：
- **SAM (Segment and Track Anything)** - 点击式精确分割
- **Grounded SAM** - 文本提示自动分割

## 🏗️ 安装目录结构

安装完成后，您的目录结构将如下：

```
/home/<USER>/SAM/
├── sam_tools/                          # SAM工具目录
│   ├── Segment-and-Track-Anything/     # SAM源码
│   └── weights/                        # SAM模型权重
│       ├── sam_vit_h_4b8939.pth       # 大模型(推荐)
│       ├── sam_vit_l_0b3195.pth       # 中模型
│       └── sam_vit_b_01ec64.pth       # 小模型
├── grounded_sam_tools/                 # Grounded SAM工具目录
│   ├── Grounded-Segment-Anything/     # Grounded SAM源码
│   └── weights/                       # Grounded SAM模型权重
│       ├── sam_vit_h_4b8939.pth       # SAM权重
│       ├── groundingdino_swint_ogc.pth # GroundingDINO权重
│       └── GroundingDINO_SwinT_OGC.py # 配置文件
├── activate_sam.sh                    # SAM环境激活脚本
├── activate_grounded_sam.sh           # Grounded SAM环境激活脚本
├── sam_quick_start.sh                 # SAM快速启动脚本
├── grounded_sam_quick_start.sh        # Grounded SAM快速启动脚本
└── mask_tools_usage.sh                # 统一使用指南
```

## 🚀 安装步骤

### 步骤1：安装SAM工具

```bash
# 进入项目目录
cd /home/<USER>/Infusion-main-cism

# 给安装脚本执行权限
chmod +x mask_creation_guide/micromamba_install_sam.sh

# 运行安装脚本
./mask_creation_guide/micromamba_install_sam.sh
```

**安装过程说明：**
- ✅ 检查micromamba环境
- 📁 创建 `/home/<USER>/SAM/sam_tools` 目录
- 📥 下载SAM源码到指定目录
- 🔧 创建 `sam_tools` micromamba环境
- 📦 安装PyTorch和相关依赖
- 📥 下载SAM模型权重文件
- 📝 创建激活和快速启动脚本

### 步骤2：安装Grounded SAM工具

```bash
# 给安装脚本执行权限
chmod +x mask_creation_guide/micromamba_install_grounded_sam.sh

# 运行安装脚本
./mask_creation_guide/micromamba_install_grounded_sam.sh
```

**安装过程说明：**
- ✅ 检查micromamba环境
- 📁 创建 `/home/<USER>/SAM/grounded_sam_tools` 目录
- 📥 下载Grounded SAM源码
- 🔧 创建 `grounded_sam_tools` micromamba环境
- 📦 安装PyTorch和相关依赖
- 📥 下载模型权重和配置文件
- 📝 创建激活和快速启动脚本

## 🎯 使用方法

### 方案A：SAM (点击式分割)

#### 1. 激活SAM环境
```bash
# 激活SAM环境
source /home/<USER>/SAM/activate_sam.sh
```

#### 2. 交互式单张图像处理
```bash
# 使用快速启动脚本
/home/<USER>/SAM/sam_quick_start.sh interactive /path/to/your/image.jpg
```

**操作说明：**
- **左键点击**：添加正点（要分割的区域）
- **右键点击**：添加负点（不要分割的区域）
- **回车键**：生成掩码
- **C键**：清除所有点
- **S键**：保存掩码

#### 3. 批量处理
```bash
# 1. 创建配置文件模板
python /home/<USER>/Infusion-main-cism/mask_creation_guide/sam_batch_processor.py --create_sample

# 2. 编辑配置文件 points_config_sample.json
# 为每张图像指定点击点坐标

# 3. 批量处理
/home/<USER>/SAM/sam_quick_start.sh batch /path/to/images /path/to/config.json
```

### 方案B：Grounded SAM (文本提示分割)

#### 1. 激活Grounded SAM环境
```bash
# 激活Grounded SAM环境
source /home/<USER>/SAM/activate_grounded_sam.sh
```

#### 2. 单张图像文本处理
```bash
# 使用快速启动脚本
/home/<USER>/SAM/grounded_sam_quick_start.sh single /path/to/image.jpg "car . person" /path/to/output_mask.jpg
```

#### 3. 批量处理
```bash
# 1. 创建文本配置文件模板
python /home/<USER>/Infusion-main-cism/mask_creation_guide/grounded_sam_creator.py --create_sample

# 2. 编辑配置文件 text_prompts_sample.json
# 为每张图像指定文本提示

# 3. 批量处理
/home/<USER>/SAM/grounded_sam_quick_start.sh batch /path/to/images /path/to/text_config.json /path/to/output_dir
```

## 📝 配置文件示例

### SAM点击配置文件 (points_config.json)
```json
{
  "DSC07956.JPG": {
    "positive_points": [[320, 240], [400, 300]],
    "negative_points": [[100, 100]]
  },
  "DSC07957.JPG": {
    "positive_points": [[250, 180]],
    "negative_points": []
  }
}
```

### Grounded SAM文本配置文件 (text_prompts.json)
```json
{
  "DSC07956.JPG": "car . vehicle",
  "DSC07957.JPG": "person . people . human",
  "DSC07958.JPG": "tree . plant . vegetation"
}
```

## 🔧 环境管理

### 查看已安装的环境
```bash
micromamba env list
```

### 手动激活环境
```bash
# 激活SAM环境
micromamba activate sam_tools

# 激活Grounded SAM环境
micromamba activate grounded_sam_tools
```

### 删除环境（如需重新安装）
```bash
# 删除SAM环境
micromamba env remove -n sam_tools -y

# 删除Grounded SAM环境
micromamba env remove -n grounded_sam_tools -y
```

## 🎯 针对InFusion项目的使用

### 为InFusion创建掩码的完整流程

1. **准备图像数据**
   ```bash
   # 确保图像在正确的目录结构中
   your_scene/
   ├── images/
   │   ├── DSC07956.JPG
   │   ├── DSC07957.JPG
   │   └── ...
   └── seg/          # 这里将存放生成的掩码
   ```

2. **选择合适的工具**
   - **精度优先**：使用SAM (点击式)
   - **效率优先**：使用Grounded SAM (文本式)

3. **生成掩码**
   ```bash
   # 使用SAM
   source /home/<USER>/activate_sam.sh
   /home/<USER>/sam_quick_start.sh batch ./your_scene/images ./points_config.json

   # 或使用Grounded SAM
   source /home/<USER>/activate_grounded_sam.sh
   /home/<USER>/grounded_sam_quick_start.sh batch ./your_scene/images ./text_config.json ./your_scene/seg
   ```

4. **验证掩码质量**
   - 检查掩码是否完整覆盖目标区域
   - 确保边界准确
   - 验证多视角一致性

## 🚨 故障排除

### 常见问题

1. **CUDA内存不足**
   ```bash
   # 使用较小的模型
   # 在脚本中将 sam_vit_h_4b8939.pth 改为 sam_vit_b_01ec64.pth
   ```

2. **环境激活失败**
   ```bash
   # 重新初始化micromamba
   micromamba shell init --shell bash
   source ~/.bashrc
   ```

3. **权重下载失败**
   ```bash
   # 手动下载权重文件到对应目录
   cd /home/<USER>/sam_tools/weights
   wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth
   ```

### 获取帮助
```bash
# 查看SAM工具帮助
python /home/<USER>/Infusion-main-cism/mask_creation_guide/sam_mask_creator.py --help

# 查看Grounded SAM工具帮助
python /home/<USER>/Infusion-main-cism/mask_creation_guide/grounded_sam_creator.py --help
```

## 🎉 安装验证

安装完成后，您可以通过以下命令验证安装：

```bash
# 检查SAM环境
source /home/<USER>/activate_sam.sh
python -c "import torch; print('PyTorch:', torch.__version__); print('CUDA available:', torch.cuda.is_available())"

# 检查Grounded SAM环境
source /home/<USER>/activate_grounded_sam.sh
python -c "import torch; print('PyTorch:', torch.__version__); print('CUDA available:', torch.cuda.is_available())"
```

现在您可以开始使用这些工具为InFusion项目创建高质量的掩码了！
