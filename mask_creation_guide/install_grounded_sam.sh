#!/bin/bash

# Grounded SAM 安装脚本

echo "开始安装 Grounded SAM..."

# 1. 克隆仓库
git clone https://github.com/IDEA-Research/Grounded-Segment-Anything.git
cd Grounded-Segment-Anything

# 2. 创建conda环境
conda create -n grounded_sam python=3.8 -y
conda activate grounded_sam

# 3. 安装PyTorch (根据你的CUDA版本调整)
pip install torch torchvision torchaudio

# 4. 安装Grounded SAM依赖
pip install -e .

# 5. 安装额外依赖
pip install opencv-python pillow numpy matplotlib
pip install transformers accelerate

# 6. 下载模型权重
mkdir weights
cd weights

# 下载SAM权重
wget https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth

# 下载GroundingDINO权重
wget https://github.com/IDEA-Research/GroundingDINO/releases/download/v0.1.0-alpha/groundingdino_swint_ogc.pth

cd ..

# 7. 下载GroundingDINO配置文件
wget https://raw.githubusercontent.com/IDEA-Research/GroundingDINO/main/groundingdino/config/GroundingDINO_SwinT_OGC.py

echo "Grounded SAM 安装完成！"
echo "激活环境: conda activate grounded_sam"
