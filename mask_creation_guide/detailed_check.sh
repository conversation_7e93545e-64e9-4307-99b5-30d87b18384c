#!/bin/bash

# 详细环境检查脚本
# 专门检查 /home/<USER>/micromamba 目录

echo "🔍 详细环境检查工具"
echo "==================="
echo ""

USER_HOME="/home/<USER>"
echo "👤 用户目录: $USER_HOME"
echo ""

echo "📁 检查可能的安装位置..."
echo ""

# 检查常见的conda/micromamba安装位置
POSSIBLE_LOCATIONS=(
    "$USER_HOME/micromamba"
    "$USER_HOME/miniconda3"
    "$USER_HOME/anaconda3"
    "$USER_HOME/.conda"
    "$USER_HOME/miniforge3"
    "/opt/miniconda3"
    "/opt/anaconda3"
    "/usr/local/bin"
)

FOUND_INSTALLATIONS=()

for location in "${POSSIBLE_LOCATIONS[@]}"; do
    if [ -d "$location" ]; then
        echo "✅ 找到目录: $location"
        
        # 检查这个目录下的可执行文件
        if [ -f "$location/bin/conda" ]; then
            echo "   🔧 找到conda: $location/bin/conda"
            FOUND_INSTALLATIONS+=("$location/bin/conda")
        fi
        
        if [ -f "$location/bin/mamba" ]; then
            echo "   🔧 找到mamba: $location/bin/mamba"
            FOUND_INSTALLATIONS+=("$location/bin/mamba")
        fi
        
        if [ -f "$location/bin/micromamba" ]; then
            echo "   🔧 找到micromamba: $location/bin/micromamba"
            FOUND_INSTALLATIONS+=("$location/bin/micromamba")
        fi
        
        if [ -f "$location/micromamba" ]; then
            echo "   🔧 找到micromamba: $location/micromamba"
            FOUND_INSTALLATIONS+=("$location/micromamba")
        fi
        
        # 列出bin目录内容
        if [ -d "$location/bin" ]; then
            echo "   📋 $location/bin 目录内容:"
            ls -la "$location/bin" | grep -E "(conda|mamba)" | head -5
        fi
        
        echo ""
    else
        echo "❌ 目录不存在: $location"
    fi
done

echo ""
echo "🔍 特别检查 /home/<USER>/micromamba..."
echo ""

MICROMAMBA_DIR="$USER_HOME/micromamba"
if [ -d "$MICROMAMBA_DIR" ]; then
    echo "✅ 确认存在: $MICROMAMBA_DIR"
    echo ""
    
    echo "📋 目录结构:"
    ls -la "$MICROMAMBA_DIR"
    echo ""
    
    # 检查bin目录
    if [ -d "$MICROMAMBA_DIR/bin" ]; then
        echo "📋 bin目录内容:"
        ls -la "$MICROMAMBA_DIR/bin"
        echo ""
        
        # 检查可执行文件
        for exe in conda mamba micromamba; do
            if [ -f "$MICROMAMBA_DIR/bin/$exe" ]; then
                echo "✅ 找到 $exe: $MICROMAMBA_DIR/bin/$exe"
                echo "   权限: $(ls -l "$MICROMAMBA_DIR/bin/$exe" | awk '{print $1}')"
                echo "   大小: $(ls -lh "$MICROMAMBA_DIR/bin/$exe" | awk '{print $5}')"
                
                # 尝试运行版本命令
                if "$MICROMAMBA_DIR/bin/$exe" --version &>/dev/null; then
                    echo "   版本: $("$MICROMAMBA_DIR/bin/$exe" --version 2>/dev/null)"
                else
                    echo "   ⚠️  版本命令失败"
                fi
                echo ""
            fi
        done
    else
        echo "❌ bin目录不存在: $MICROMAMBA_DIR/bin"
    fi
    
    # 检查是否有直接的micromamba可执行文件
    if [ -f "$MICROMAMBA_DIR/micromamba" ]; then
        echo "✅ 找到直接可执行文件: $MICROMAMBA_DIR/micromamba"
        echo "   权限: $(ls -l "$MICROMAMBA_DIR/micromamba" | awk '{print $1}')"
        echo "   大小: $(ls -lh "$MICROMAMBA_DIR/micromamba" | awk '{print $5}')"
        
        # 尝试运行
        if "$MICROMAMBA_DIR/micromamba" --version &>/dev/null; then
            echo "   版本: $("$MICROMAMBA_DIR/micromamba" --version 2>/dev/null)"
        else
            echo "   ⚠️  版本命令失败"
        fi
        echo ""
    fi
    
else
    echo "❌ 目录不存在: $MICROMAMBA_DIR"
    echo ""
    echo "🔍 搜索整个用户目录中的micromamba..."
    find "$USER_HOME" -name "*micromamba*" -type f 2>/dev/null | head -10
fi

echo ""
echo "🔍 检查当前PATH..."
echo ""
echo "当前PATH:"
echo "$PATH" | tr ':' '\n' | nl
echo ""

echo "🔍 检查shell配置文件..."
echo ""

SHELL_CONFIGS=(
    "$USER_HOME/.bashrc"
    "$USER_HOME/.bash_profile" 
    "$USER_HOME/.zshrc"
    "$USER_HOME/.profile"
)

for config in "${SHELL_CONFIGS[@]}"; do
    if [ -f "$config" ]; then
        echo "📄 检查 $config:"
        echo "   文件大小: $(wc -l < "$config") 行"
        
        if grep -q "micromamba\|conda\|mamba" "$config" 2>/dev/null; then
            echo "   ✅ 包含conda/mamba相关配置:"
            grep -n "micromamba\|conda\|mamba" "$config" | head -5
        else
            echo "   ❌ 未找到conda/mamba配置"
        fi
        echo ""
    else
        echo "❌ 配置文件不存在: $config"
    fi
done

echo ""
echo "🔍 检查环境变量..."
echo ""
echo "CONDA_DEFAULT_ENV: ${CONDA_DEFAULT_ENV:-'未设置'}"
echo "CONDA_PREFIX: ${CONDA_PREFIX:-'未设置'}"
echo "CONDA_EXE: ${CONDA_EXE:-'未设置'}"
echo "MAMBA_EXE: ${MAMBA_EXE:-'未设置'}"
echo ""

echo "🔍 尝试手动测试找到的安装..."
echo ""

if [ ${#FOUND_INSTALLATIONS[@]} -gt 0 ]; then
    echo "✅ 找到 ${#FOUND_INSTALLATIONS[@]} 个可能的安装:"
    
    for installation in "${FOUND_INSTALLATIONS[@]}"; do
        echo ""
        echo "🧪 测试: $installation"
        
        if [ -f "$installation" ]; then
            echo "   ✅ 文件存在"
            echo "   权限: $(ls -l "$installation" | awk '{print $1}')"
            
            # 测试版本命令
            if "$installation" --version &>/dev/null; then
                version=$("$installation" --version 2>/dev/null)
                echo "   ✅ 版本: $version"
                
                # 测试环境列表命令
                if "$installation" env list &>/dev/null; then
                    echo "   ✅ 环境列表命令正常"
                    echo "   📋 环境列表:"
                    "$installation" env list 2>/dev/null | head -5
                else
                    echo "   ⚠️  环境列表命令失败"
                fi
            else
                echo "   ❌ 版本命令失败"
            fi
        else
            echo "   ❌ 文件不存在"
        fi
    done
else
    echo "❌ 未找到任何conda/mamba/micromamba安装"
fi

echo ""
echo "💡 解决建议:"
echo ""

if [ ${#FOUND_INSTALLATIONS[@]} -gt 0 ]; then
    echo "✅ 找到了conda环境管理器，但PATH配置有问题"
    echo ""
    echo "🔧 解决方案1 - 添加到PATH:"
    
    for installation in "${FOUND_INSTALLATIONS[@]}"; do
        bin_dir=$(dirname "$installation")
        echo "   export PATH=\"$bin_dir:\$PATH\""
    done
    
    echo ""
    echo "🔧 解决方案2 - 初始化shell:"
    for installation in "${FOUND_INSTALLATIONS[@]}"; do
        echo "   $installation init bash"
    done
    
    echo ""
    echo "🔧 解决方案3 - 创建符号链接:"
    for installation in "${FOUND_INSTALLATIONS[@]}"; do
        exe_name=$(basename "$installation")
        echo "   sudo ln -sf $installation /usr/local/bin/$exe_name"
    done
    
else
    echo "❌ 确实没有找到conda环境管理器"
    echo ""
    echo "🔧 建议重新安装:"
    echo "1. 下载Miniconda:"
    echo "   wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
    echo "   bash Miniconda3-latest-Linux-x86_64.sh"
    echo ""
    echo "2. 或下载micromamba:"
    echo "   curl -Ls https://micro.mamba.pm/api/micromamba/linux-64/latest | tar -xvj bin/micromamba"
    echo "   mkdir -p $USER_HOME/micromamba/bin"
    echo "   mv bin/micromamba $USER_HOME/micromamba/bin/"
fi

echo ""
echo "🎯 检查完成！"
