#!/bin/bash

# 重新创建干净的SAM环境

echo "🔧 重新创建SAM环境"
echo "=================="
echo ""

# 设置PATH
export PATH="/home/<USER>/bin:$PATH"

# 清理环境变量
unset CONDA_DEFAULT_ENV
unset CONDA_PREFIX
unset CONDA_PYTHON_EXE
unset CONDA_EXE
unset PYTHONPATH

echo "🗑️  删除现有的sam_tools环境..."
micromamba env remove -n sam_tools -y

echo "🔧 创建新的sam_tools环境..."
micromamba create -n sam_tools python=3.8 -y

echo "🔄 激活新环境..."
micromamba activate sam_tools

echo "📦 安装PyTorch..."
# 使用conda-forge安装PyTorch以避免冲突
micromamba install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y

echo "📦 安装其他依赖..."
pip install opencv-python
pip install pillow
pip install numpy
pip install matplotlib
pip install segment-anything

echo "🧪 测试环境..."
python -c "
import torch
print('✅ PyTorch版本:', torch.__version__)
print('✅ CUDA可用:', torch.cuda.is_available())

import cv2
print('✅ OpenCV版本:', cv2.__version__)

from segment_anything import SamPredictor, sam_model_registry
print('✅ Segment Anything导入成功')
"

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 SAM环境重新创建成功！"
    echo ""
    echo "🚀 现在可以使用SAM工具了"
else
    echo ""
    echo "❌ 环境创建失败"
    exit 1
fi
