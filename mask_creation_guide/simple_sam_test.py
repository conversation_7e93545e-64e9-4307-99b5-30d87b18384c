#!/usr/bin/env python3
"""
简化的SAM测试脚本 - 使用现有环境
"""

import os
import sys
import cv2
import numpy as np

def create_simple_mask():
    """创建一个简单的测试掩码，不依赖SAM库"""
    
    print("🎨 创建简单测试掩码（不使用SAM）...")
    
    image_path = "/home/<USER>/Infusion-main-cism/data/rednet/images/00000.png"
    output_dir = "/home/<USER>/Infusion-main-cism/data/rednet/seg"
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图像: {image_path}")
            return False
            
        height, width = image.shape[:2]
        print(f"✅ 图像尺寸: {width}x{height}")
        
        # 创建几种不同的测试掩码
        
        # 1. 中心矩形掩码
        mask1 = np.zeros((height, width), dtype=np.uint8)
        center_x, center_y = width // 2, height // 2
        mask_w, mask_h = width // 4, height // 4
        x1, y1 = center_x - mask_w // 2, center_y - mask_h // 2
        x2, y2 = center_x + mask_w // 2, center_y + mask_h // 2
        mask1[y1:y2, x1:x2] = 255
        
        output_path1 = os.path.join(output_dir, "00000_center_mask.png")
        cv2.imwrite(output_path1, mask1)
        print(f"✅ 中心矩形掩码: {output_path1}")
        
        # 2. 圆形掩码
        mask2 = np.zeros((height, width), dtype=np.uint8)
        radius = min(width, height) // 6
        cv2.circle(mask2, (center_x, center_y), radius, 255, -1)
        
        output_path2 = os.path.join(output_dir, "00000_circle_mask.png")
        cv2.imwrite(output_path2, mask2)
        print(f"✅ 圆形掩码: {output_path2}")
        
        # 3. 左上角掩码
        mask3 = np.zeros((height, width), dtype=np.uint8)
        mask3[0:height//3, 0:width//3] = 255
        
        output_path3 = os.path.join(output_dir, "00000_corner_mask.png")
        cv2.imwrite(output_path3, mask3)
        print(f"✅ 左上角掩码: {output_path3}")
        
        # 4. 边缘检测掩码（基于图像内容）
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        
        # 膨胀边缘以创建掩码区域
        kernel = np.ones((5,5), np.uint8)
        mask4 = cv2.dilate(edges, kernel, iterations=2)
        
        output_path4 = os.path.join(output_dir, "00000_edge_mask.png")
        cv2.imwrite(output_path4, mask4)
        print(f"✅ 边缘检测掩码: {output_path4}")
        
        # 5. 创建标准的00000.png掩码（与原图同名）
        # 使用中心矩形作为默认掩码
        output_path_standard = os.path.join(output_dir, "00000.png")
        cv2.imwrite(output_path_standard, mask1)
        print(f"✅ 标准掩码文件: {output_path_standard}")
        
        print(f"\n📊 掩码统计:")
        print(f"   图像尺寸: {width}x{height}")
        print(f"   掩码文件数: 5个")
        print(f"   输出目录: {output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建掩码失败: {e}")
        return False

def show_usage():
    """显示使用说明"""
    print("\n💡 掩码使用说明:")
    print("================")
    print("1. 白色区域(255) = 需要修复的部分")
    print("2. 黑色区域(0) = 保留的部分")
    print("3. 掩码文件名应与原图相同")
    print("4. 掩码分辨率应与原图一致")
    
    print("\n🔧 如果需要精确掩码，可以:")
    print("1. 使用图像编辑软件手动创建")
    print("2. 修复SAM环境后使用自动工具")
    print("3. 使用在线SAM工具")
    
    print("\n📁 生成的掩码文件:")
    output_dir = "/home/<USER>/Infusion-main-cism/data/rednet/seg"
    if os.path.exists(output_dir):
        files = [f for f in os.listdir(output_dir) if f.endswith('.png')]
        for f in files:
            print(f"   - {f}")

def main():
    print("🎯 简化SAM掩码创建工具")
    print("======================")
    print("")
    
    # 检查图像文件
    image_path = "/home/<USER>/Infusion-main-cism/data/rednet/images/00000.png"
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return
    
    print(f"✅ 图像文件存在: {image_path}")
    
    # 创建测试掩码
    if create_simple_mask():
        print("\n🎉 掩码创建成功！")
        show_usage()
        
        print("\n🚀 下一步:")
        print("1. 检查生成的掩码文件")
        print("2. 选择最适合的掩码")
        print("3. 如需要，可以手动编辑掩码")
        print("4. 将掩码用于InFusion项目")
        
    else:
        print("\n❌ 掩码创建失败")

if __name__ == "__main__":
    main()
