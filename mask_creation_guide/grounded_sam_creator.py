#!/usr/bin/env python3
"""
Grounded SAM 掩码创建工具
使用文本提示自动生成掩码
"""

import cv2
import numpy as np
import torch
import argparse
import os
from pathlib import Path
import json

# Grounded SAM imports
from groundingdino.models import build_model
from groundingdino.util.slconfig import SLConfig
from groundingdino.util.utils import clean_state_dict, get_phrases_from_posmap
from segment_anything import sam_model_registry, SamPredictor
import groundingdino.datasets.transforms as T

class GroundedSAMCreator:
    def __init__(self, sam_checkpoint, grounding_dino_config, grounding_dino_checkpoint):
        """
        初始化Grounded SAM
        
        Args:
            sam_checkpoint: SAM模型权重路径
            grounding_dino_config: GroundingDINO配置文件路径
            grounding_dino_checkpoint: GroundingDINO权重路径
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 加载GroundingDINO
        print("加载GroundingDINO模型...")
        args = SLConfig.fromfile(grounding_dino_config)
        args.device = self.device
        self.grounding_model = build_model(args)
        checkpoint = torch.load(grounding_dino_checkpoint, map_location="cpu")
        load_res = self.grounding_model.load_state_dict(
            clean_state_dict(checkpoint["model"]), strict=False
        )
        print(f"GroundingDINO加载结果: {load_res}")
        self.grounding_model.eval()
        
        # 加载SAM
        print("加载SAM模型...")
        sam = sam_model_registry["vit_h"](checkpoint=sam_checkpoint)
        sam.to(device=self.device)
        self.sam_predictor = SamPredictor(sam)
        
        # 图像预处理
        self.transform = T.Compose([
            T.RandomResize([800], max_size=1333),
            T.ToTensor(),
            T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        ])
    
    def load_image(self, image_path):
        """加载图像"""
        image = cv2.imread(image_path)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        return image_rgb
    
    def detect_objects(self, image, text_prompt, box_threshold=0.3, text_threshold=0.25):
        """
        使用GroundingDINO检测物体
        
        Args:
            image: 输入图像 (RGB格式)
            text_prompt: 文本提示，如 "car . person . tree"
            box_threshold: 边界框阈值
            text_threshold: 文本匹配阈值
        """
        # 预处理图像
        image_tensor, _ = self.transform(image, None)
        
        # 检测
        with torch.no_grad():
            outputs = self.grounding_model(image_tensor[None].to(self.device), captions=[text_prompt])
        
        # 解析结果
        prediction_logits = outputs["pred_logits"].cpu().sigmoid()[0]  # (nq, 256)
        prediction_boxes = outputs["pred_boxes"].cpu()[0]  # (nq, 4)
        
        # 过滤结果
        logits_filt = prediction_logits.clone()
        boxes_filt = prediction_boxes.clone()
        filt_mask = logits_filt.max(dim=1)[0] > box_threshold
        logits_filt = logits_filt[filt_mask]  # num_filt, 256
        boxes_filt = boxes_filt[filt_mask]  # num_filt, 4
        
        # 获取短语
        tokenlizer = self.grounding_model.tokenizer
        tokenized = tokenlizer(text_prompt)
        
        # 构建预测短语
        pred_phrases = []
        for logit, box in zip(logits_filt, boxes_filt):
            pred_phrase = get_phrases_from_posmap(logit > text_threshold, tokenized, tokenlizer)
            pred_phrases.append(pred_phrase + f"({str(logit.max().item())[:4]})")
        
        return boxes_filt, pred_phrases
    
    def create_mask_from_text(self, image_path, text_prompt, output_path, 
                             box_threshold=0.3, text_threshold=0.25):
        """
        根据文本提示创建掩码
        
        Args:
            image_path: 输入图像路径
            text_prompt: 文本提示
            output_path: 输出掩码路径
            box_threshold: 边界框阈值
            text_threshold: 文本匹配阈值
        """
        # 加载图像
        image = self.load_image(image_path)
        H, W = image.shape[:2]
        
        # 检测物体
        boxes_filt, pred_phrases = self.detect_objects(
            image, text_prompt, box_threshold, text_threshold
        )
        
        if len(boxes_filt) == 0:
            print(f"未检测到匹配的物体: {text_prompt}")
            return False
        
        print(f"检测到 {len(boxes_filt)} 个物体: {pred_phrases}")
        
        # 转换边界框格式
        boxes_filt = boxes_filt * torch.Tensor([W, H, W, H])
        boxes_xyxy = box_convert(boxes=boxes_filt, in_fmt="cxcywh", out_fmt="xyxy").numpy()
        
        # 使用SAM生成掩码
        self.sam_predictor.set_image(image)
        
        # 合并所有掩码
        final_mask = np.zeros((H, W), dtype=bool)
        
        for box in boxes_xyxy:
            masks, _, _ = self.sam_predictor.predict(
                point_coords=None,
                point_labels=None,
                box=box[None, :],
                multimask_output=False,
            )
            final_mask = final_mask | masks[0]
        
        # 保存掩码
        mask_image = (final_mask * 255).astype(np.uint8)
        cv2.imwrite(output_path, mask_image)
        
        print(f"掩码已保存到: {output_path}")
        return True

def box_convert(boxes, in_fmt, out_fmt):
    """边界框格式转换"""
    if in_fmt == "cxcywh" and out_fmt == "xyxy":
        x_c, y_c, w, h = boxes.unbind(-1)
        b = [x_c - 0.5 * w, y_c - 0.5 * h, x_c + 0.5 * w, y_c + 0.5 * h]
        return torch.stack(b, dim=-1)
    else:
        raise NotImplementedError(f"Conversion from {in_fmt} to {out_fmt} not implemented")

class GroundedSAMBatchProcessor:
    def __init__(self, sam_checkpoint, grounding_dino_config, grounding_dino_checkpoint):
        """初始化批量处理器"""
        self.creator = GroundedSAMCreator(
            sam_checkpoint, grounding_dino_config, grounding_dino_checkpoint
        )
    
    def process_batch_from_config(self, config_path, images_dir, output_dir):
        """
        根据配置文件批量处理
        
        配置文件格式:
        {
            "image1.jpg": "car . person",
            "image2.jpg": "tree . building"
        }
        """
        # 加载配置
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 处理每张图像
        for image_name, text_prompt in config.items():
            image_path = os.path.join(images_dir, image_name)
            output_path = os.path.join(output_dir, image_name)
            
            if not os.path.exists(image_path):
                print(f"图像不存在: {image_path}")
                continue
            
            print(f"处理: {image_name} -> '{text_prompt}'")
            try:
                success = self.creator.create_mask_from_text(
                    image_path, text_prompt, output_path
                )
                if success:
                    print(f"✓ 成功处理: {image_name}")
                else:
                    print(f"✗ 处理失败: {image_name}")
            except Exception as e:
                print(f"✗ 处理出错 {image_name}: {e}")

def create_sample_text_config():
    """创建示例文本配置文件"""
    sample_config = {
        "example_image.jpg": "car . person",
        "another_image.jpg": "tree . building . road",
        "scene.jpg": "remove the chair . table"
    }
    
    with open("text_prompts_sample.json", 'w') as f:
        json.dump(sample_config, f, indent=2)
    
    print("示例文本配置文件已创建: text_prompts_sample.json")

def main():
    parser = argparse.ArgumentParser(description="Grounded SAM掩码创建工具")
    parser.add_argument("--sam_checkpoint", required=True, help="SAM模型权重路径")
    parser.add_argument("--grounding_dino_config", required=True, help="GroundingDINO配置文件路径")
    parser.add_argument("--grounding_dino_checkpoint", required=True, help="GroundingDINO权重路径")
    parser.add_argument("--image_path", help="单张图像路径")
    parser.add_argument("--text_prompt", help="文本提示")
    parser.add_argument("--output_path", help="输出掩码路径")
    parser.add_argument("--images_dir", help="批量处理: 图像目录")
    parser.add_argument("--config_path", help="批量处理: 文本配置文件")
    parser.add_argument("--output_dir", help="批量处理: 输出目录")
    parser.add_argument("--create_sample", action="store_true", help="创建示例配置文件")
    parser.add_argument("--box_threshold", type=float, default=0.3, help="边界框阈值")
    parser.add_argument("--text_threshold", type=float, default=0.25, help="文本匹配阈值")
    
    args = parser.parse_args()
    
    if args.create_sample:
        create_sample_text_config()
        return
    
    # 单张图像处理
    if args.image_path and args.text_prompt and args.output_path:
        creator = GroundedSAMCreator(
            args.sam_checkpoint, 
            args.grounding_dino_config, 
            args.grounding_dino_checkpoint
        )
        creator.create_mask_from_text(
            args.image_path, 
            args.text_prompt, 
            args.output_path,
            args.box_threshold,
            args.text_threshold
        )
    
    # 批量处理
    elif args.images_dir and args.config_path and args.output_dir:
        processor = GroundedSAMBatchProcessor(
            args.sam_checkpoint,
            args.grounding_dino_config,
            args.grounding_dino_checkpoint
        )
        processor.process_batch_from_config(
            args.config_path, 
            args.images_dir, 
            args.output_dir
        )
    
    else:
        print("请提供正确的参数组合")
        print("单张处理: --image_path, --text_prompt, --output_path")
        print("批量处理: --images_dir, --config_path, --output_dir")

if __name__ == "__main__":
    main()
