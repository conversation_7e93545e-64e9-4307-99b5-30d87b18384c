#!/usr/bin/env python3
"""
Stage 1 渲染质量自动检查脚本
使用方法: python check_stage1_quality.py --output_dir outputs/output/stage1_rednet --data_dir data/rednet
"""

import os
import cv2
import numpy as np
import json
import argparse
from PIL import Image
from plyfile import PlyData
import matplotlib.pyplot as plt

def check_file_completeness(output_dir, data_dir):
    """检查输出文件完整性"""
    print("🔍 检查文件完整性...")
    
    # 检查关键目录和文件
    required_paths = [
        "point_cloud/iteration_30000/point_cloud.ply",
        "cameras.json",
        "train/ours_30000/renders/",
        "train/ours_30000/depth_dis/"
    ]
    
    missing_files = []
    for path in required_paths:
        full_path = os.path.join(output_dir, path)
        if not os.path.exists(full_path):
            missing_files.append(path)
    
    if missing_files:
        print(f"❌ 缺失文件: {missing_files}")
        return False
    else:
        print("✅ 所有必需文件都存在")
        return True

def check_point_cloud_quality(output_dir):
    """检查点云质量"""
    print("\n🔍 检查点云质量...")
    
    ply_path = os.path.join(output_dir, "point_cloud/iteration_30000/point_cloud.ply")
    
    try:
        ply_data = PlyData.read(ply_path)
        vertex_data = ply_data['vertex']
        num_points = len(vertex_data.data)
        num_properties = len(vertex_data.properties)
        
        print(f"点云点数: {num_points:,}")
        print(f"点云属性数: {num_properties}")
        
        # 检查17通道结构
        if num_properties == 17:
            print("✅ 点云结构正确 (17通道)")
        else:
            print(f"⚠️ 点云结构异常 (期望17通道，实际{num_properties}通道)")
        
        # 检查点云密度
        if num_points > 100000:
            print("✅ 点云密度充足")
            density_score = "优秀"
        elif num_points > 50000:
            print("⚠️ 点云密度中等")
            density_score = "良好"
        else:
            print("❌ 点云密度可能不足")
            density_score = "需要改进"
            
        return {
            "num_points": num_points,
            "num_properties": num_properties,
            "density_score": density_score,
            "structure_correct": num_properties == 17
        }
        
    except Exception as e:
        print(f"❌ 点云文件读取失败: {e}")
        return None

def check_render_quality(output_dir, data_dir):
    """检查渲染质量"""
    print("\n🔍 检查渲染质量...")
    
    renders_dir = os.path.join(output_dir, "train/ours_30000/renders")
    images_dir = os.path.join(data_dir, "images")
    
    if not os.path.exists(renders_dir):
        print("❌ 渲染结果目录不存在")
        return None
    
    render_files = [f for f in os.listdir(renders_dir) if f.endswith('.png')]
    image_files = [f for f in os.listdir(images_dir) if f.endswith('.png')]
    
    print(f"渲染图像数量: {len(render_files)}")
    print(f"原始图像数量: {len(image_files)}")
    
    if len(render_files) != len(image_files):
        print("⚠️ 渲染图像数量与原始图像不匹配")
    
    # 随机选择几张图像进行质量检查
    sample_files = render_files[:min(5, len(render_files))]
    quality_scores = []
    
    for filename in sample_files:
        try:
            # 加载图像
            original_path = os.path.join(images_dir, filename)
            rendered_path = os.path.join(renders_dir, filename)
            
            if not os.path.exists(original_path):
                continue
                
            original = cv2.imread(original_path)
            rendered = cv2.imread(rendered_path)
            
            if original is None or rendered is None:
                continue
            
            # 计算PSNR
            mse = np.mean((original.astype(float) - rendered.astype(float)) ** 2)
            if mse == 0:
                psnr = float('inf')
            else:
                psnr = 20 * np.log10(255.0 / np.sqrt(mse))
            
            # 计算SSIM (简化版本)
            def ssim_simple(img1, img2):
                mu1 = np.mean(img1)
                mu2 = np.mean(img2)
                sigma1 = np.var(img1)
                sigma2 = np.var(img2)
                sigma12 = np.mean((img1 - mu1) * (img2 - mu2))
                
                c1 = (0.01 * 255) ** 2
                c2 = (0.03 * 255) ** 2
                
                ssim = ((2 * mu1 * mu2 + c1) * (2 * sigma12 + c2)) / \
                       ((mu1**2 + mu2**2 + c1) * (sigma1 + sigma2 + c2))
                return ssim
            
            # 转换为灰度图计算SSIM
            gray1 = cv2.cvtColor(original, cv2.COLOR_BGR2GRAY)
            gray2 = cv2.cvtColor(rendered, cv2.COLOR_BGR2GRAY)
            ssim = ssim_simple(gray1, gray2)
            
            quality_scores.append({
                'filename': filename,
                'psnr': psnr,
                'ssim': ssim
            })
            
            print(f"{filename}: PSNR={psnr:.2f}dB, SSIM={ssim:.3f}")
            
        except Exception as e:
            print(f"处理 {filename} 时出错: {e}")
    
    if quality_scores:
        avg_psnr = np.mean([s['psnr'] for s in quality_scores if s['psnr'] != float('inf')])
        avg_ssim = np.mean([s['ssim'] for s in quality_scores])
        
        print(f"\n平均PSNR: {avg_psnr:.2f}dB")
        print(f"平均SSIM: {avg_ssim:.3f}")
        
        # 质量评级
        if avg_psnr > 30 and avg_ssim > 0.95:
            quality_grade = "优秀"
            print("✅ 渲染质量优秀")
        elif avg_psnr > 25 and avg_ssim > 0.90:
            quality_grade = "良好"
            print("⚠️ 渲染质量良好")
        else:
            quality_grade = "需要改进"
            print("❌ 渲染质量需要改进")
        
        return {
            "avg_psnr": avg_psnr,
            "avg_ssim": avg_ssim,
            "quality_grade": quality_grade,
            "sample_count": len(quality_scores)
        }
    
    return None

def check_depth_quality(output_dir):
    """检查深度图质量"""
    print("\n🔍 检查深度图质量...")
    
    depth_dir = os.path.join(output_dir, "train/ours_30000/depth_dis")
    
    if not os.path.exists(depth_dir):
        print("❌ 深度图目录不存在")
        return None
    
    depth_files = [f for f in os.listdir(depth_dir) if f.endswith('.npy')]
    print(f"深度图文件数量: {len(depth_files)}")
    
    if len(depth_files) == 0:
        print("❌ 没有找到深度图文件")
        return None
    
    # 检查几个深度图的质量
    sample_files = depth_files[:min(5, len(depth_files))]
    depth_stats = []
    
    for filename in sample_files:
        try:
            depth_path = os.path.join(depth_dir, filename)
            depth = np.load(depth_path)
            
            valid_pixels = (depth > 0).sum()
            total_pixels = depth.size
            coverage = valid_pixels / total_pixels * 100
            
            depth_range = depth.max() - depth.min() if valid_pixels > 0 else 0
            depth_mean = depth[depth > 0].mean() if valid_pixels > 0 else 0
            
            depth_stats.append({
                'filename': filename,
                'coverage': coverage,
                'range': depth_range,
                'mean': depth_mean
            })
            
            print(f"{filename}: 覆盖率={coverage:.1f}%, 深度范围={depth_range:.3f}")
            
        except Exception as e:
            print(f"处理 {filename} 时出错: {e}")
    
    if depth_stats:
        avg_coverage = np.mean([s['coverage'] for s in depth_stats])
        print(f"\n平均深度覆盖率: {avg_coverage:.1f}%")
        
        if avg_coverage > 95:
            depth_grade = "优秀"
            print("✅ 深度图质量优秀")
        elif avg_coverage > 85:
            depth_grade = "良好"
            print("⚠️ 深度图质量良好")
        else:
            depth_grade = "需要改进"
            print("❌ 深度图质量需要改进")
        
        return {
            "avg_coverage": avg_coverage,
            "depth_grade": depth_grade,
            "sample_count": len(depth_stats)
        }
    
    return None

def generate_quality_report(output_dir, data_dir):
    """生成完整的质量报告"""
    print("📊 生成Stage 1质量报告")
    print("=" * 50)
    
    # 执行各项检查
    file_check = check_file_completeness(output_dir, data_dir)
    point_cloud_check = check_point_cloud_quality(output_dir)
    render_check = check_render_quality(output_dir, data_dir)
    depth_check = check_depth_quality(output_dir)
    
    # 生成总结报告
    print("\n" + "=" * 50)
    print("📋 质量评估总结")
    print("=" * 50)
    
    overall_score = 0
    max_score = 4
    
    if file_check:
        print("✅ 文件完整性: 通过")
        overall_score += 1
    else:
        print("❌ 文件完整性: 失败")
    
    if point_cloud_check and point_cloud_check['structure_correct']:
        print(f"✅ 点云质量: {point_cloud_check['density_score']}")
        if point_cloud_check['density_score'] == "优秀":
            overall_score += 1
        elif point_cloud_check['density_score'] == "良好":
            overall_score += 0.7
    else:
        print("❌ 点云质量: 失败")
    
    if render_check:
        print(f"✅ 渲染质量: {render_check['quality_grade']}")
        if render_check['quality_grade'] == "优秀":
            overall_score += 1
        elif render_check['quality_grade'] == "良好":
            overall_score += 0.7
    else:
        print("❌ 渲染质量: 失败")
    
    if depth_check:
        print(f"✅ 深度图质量: {depth_check['depth_grade']}")
        if depth_check['depth_grade'] == "优秀":
            overall_score += 1
        elif depth_check['depth_grade'] == "良好":
            overall_score += 0.7
    else:
        print("❌ 深度图质量: 失败")
    
    # 总体评分
    overall_percentage = (overall_score / max_score) * 100
    print(f"\n🎯 总体质量评分: {overall_percentage:.1f}%")
    
    if overall_percentage >= 90:
        print("🎉 Stage 1训练结果优秀，可以进行Stage 2！")
    elif overall_percentage >= 70:
        print("⚠️ Stage 1训练结果良好，建议检查后进行Stage 2")
    else:
        print("❌ Stage 1训练结果需要改进，建议重新训练")
    
    return overall_percentage

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Stage 1质量检查工具")
    parser.add_argument("--output_dir", default="outputs/output/stage1_rednet", 
                       help="Stage 1输出目录")
    parser.add_argument("--data_dir", default="data/rednet", 
                       help="原始数据目录")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.output_dir):
        print(f"❌ 输出目录不存在: {args.output_dir}")
        exit(1)
    
    if not os.path.exists(args.data_dir):
        print(f"❌ 数据目录不存在: {args.data_dir}")
        exit(1)
    
    # 执行质量检查
    score = generate_quality_report(args.output_dir, args.data_dir)
